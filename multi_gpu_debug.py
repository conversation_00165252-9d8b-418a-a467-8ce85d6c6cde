#!/usr/bin/env python3
"""
多GPU调试脚本 - 检测可用GPU并使用多卡分布式加载模型
"""

import os
import torch
import subprocess
from vllm import LLM, SamplingParams

def check_available_gpus():
    """检查可用的GPU数量和状态"""
    print("=== GPU可用性检查 ===")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return 0
    
    gpu_count = torch.cuda.device_count()
    print(f"检测到 {gpu_count} 张GPU卡")
    
    # 检查每张GPU的详细信息
    available_gpus = []
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        
        # 检查当前内存使用情况
        torch.cuda.set_device(i)
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        free_memory = total_memory - allocated
        
        print(f"\nGPU {i}: {props.name}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  已保留: {reserved:.2f} GB")
        print(f"  可用内存: {free_memory:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")
        
        # 如果可用内存超过10GB，认为可用
        if free_memory > 10.0:
            available_gpus.append(i)
            print(f"  ✅ GPU {i} 可用于模型加载")
        else:
            print(f"  ⚠️ GPU {i} 内存不足")
    
    print(f"\n可用于模型加载的GPU: {available_gpus}")
    return len(available_gpus), available_gpus

def check_nvidia_smi():
    """使用nvidia-smi检查GPU状态"""
    print("\n=== nvidia-smi 输出 ===")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        print(result.stdout)
        
        # 检查GPU进程
        print("\n=== GPU进程信息 ===")
        result = subprocess.run(['nvidia-smi', 'pmon', '-c', '1'], capture_output=True, text=True)
        print(result.stdout)
        
    except FileNotFoundError:
        print("nvidia-smi 命令不可用")

def load_model_multi_gpu(model_name, num_gpus, available_gpus):
    """使用多GPU加载模型"""
    
    # 根据可用GPU数量设计配置
    configs = [
        {
            "name": f"多GPU配置 ({num_gpus}张卡)",
            "tensor_parallel_size": min(num_gpus, 4),  # 最多使用4张卡
            "gpu_memory_utilization": 0.85,
            "max_model_len": 1024,
            "enable_prefix_caching": True,
        },
        {
            "name": f"保守多GPU配置 ({min(num_gpus, 2)}张卡)",
            "tensor_parallel_size": min(num_gpus, 2),  # 最多使用2张卡
            "gpu_memory_utilization": 0.75,
            "max_model_len": 512,
            "enable_prefix_caching": False,
        },
        {
            "name": "单GPU配置",
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.65,
            "max_model_len": 256,
            "enable_prefix_caching": False,
        }
    ]
    
    for config in configs:
        print(f"\n=== 尝试{config['name']} ===")
        print(f"tensor_parallel_size: {config['tensor_parallel_size']}")
        print(f"gpu_memory_utilization: {config['gpu_memory_utilization']}")
        print(f"max_model_len: {config['max_model_len']}")
        
        try:
            # 清理GPU内存
            torch.cuda.empty_cache()
            
            # 设置可见的GPU
            if config['tensor_parallel_size'] > 1:
                visible_gpus = available_gpus[:config['tensor_parallel_size']]
                os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(map(str, visible_gpus))
                print(f"使用GPU: {visible_gpus}")
            
            llm = LLM(
                model=model_name,
                tensor_parallel_size=config["tensor_parallel_size"],
                max_model_len=config["max_model_len"],
                enable_prefix_caching=config["enable_prefix_caching"],
                trust_remote_code=True,
                gpu_memory_utilization=config["gpu_memory_utilization"],
                enforce_eager=True,  # 对多GPU更稳定
                disable_custom_all_reduce=False,  # 多GPU时可以启用
            )
            
            print(f"✅ {config['name']}加载成功！")
            return llm, config
            
        except Exception as e:
            print(f"❌ {config['name']}失败: {e}")
            torch.cuda.empty_cache()
            continue
    
    return None, None

def test_multi_gpu_inference(llm, config):
    """测试多GPU推理"""
    print(f"\n=== 测试多GPU推理 (使用{config['tensor_parallel_size']}张卡) ===")
    
    prompts = [
        "Hello, how are you?",
        "What is artificial intelligence?",
        "Explain quantum computing in simple terms."
    ]
    
    sampling_params = SamplingParams(
        temperature=0.0,
        top_p=1.0,
        max_tokens=50
    )
    
    try:
        outputs = llm.generate(prompts, sampling_params)
        
        for i, output in enumerate(outputs):
            print(f"\n--- 测试 {i+1} ---")
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        return True
        
    except Exception as e:
        print(f"多GPU推理失败: {e}")
        return False

def main():
    """主函数"""
    print("多GPU调试工具")
    print("=" * 60)
    
    # 检查GPU状态
    check_nvidia_smi()
    
    # 检查可用GPU
    num_gpus, available_gpus = check_available_gpus()
    
    if num_gpus == 0:
        print("❌ 没有可用的GPU，无法继续")
        return False
    
    print(f"\n🎯 将使用 {num_gpus} 张GPU进行模型加载")
    
    # 模型路径
    model_name = "/home/<USER>/Qwen-QwQ-32B-W8A8-SmoothQuant"
    
    # 尝试多GPU加载
    llm, config = load_model_multi_gpu(model_name, num_gpus, available_gpus)
    
    if llm is None:
        print("\n❌ 所有多GPU配置都失败了")
        return False
    
    # 测试推理
    success = test_multi_gpu_inference(llm, config)
    
    if success:
        print(f"\n🎉 多GPU模型加载和推理都成功！")
        print(f"最佳配置: {config['name']}")
        print(f"使用GPU数量: {config['tensor_parallel_size']}")
    
    # 最终GPU状态
    print(f"\n=== 最终GPU内存状态 ===")
    check_available_gpus()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
