#!/usr/bin/env python3
"""
GPU内存调试和优化脚本
用于诊断和解决vLLM GPU内存不足问题
"""

import os
import torch
import psutil
import subprocess
from vllm import LLM, SamplingParams

def check_gpu_memory():
    """检查GPU内存状态"""
    print("=== GPU内存状态检查 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用")
        return False
    
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        free_memory = total_memory - allocated
        
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  已保留: {reserved:.2f} GB")
        print(f"  可用内存: {free_memory:.2f} GB")
        print(f"  内存利用率: {allocated/total_memory*100:.1f}%")
        print()
    
    return True

def check_system_memory():
    """检查系统内存状态"""
    print("=== 系统内存状态检查 ===")
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / 1024**3:.2f} GB")
    print(f"可用内存: {memory.available / 1024**3:.2f} GB")
    print(f"使用率: {memory.percent:.1f}%")
    print()

def clear_gpu_memory():
    """清理GPU内存"""
    print("=== 清理GPU内存 ===")
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU缓存已清理")
    else:
        print("CUDA不可用，无法清理GPU内存")

def check_gpu_processes():
    """检查占用GPU的进程"""
    print("=== 检查GPU进程 ===")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        print(result.stdout)
    except FileNotFoundError:
        print("nvidia-smi命令不可用")

def load_model_with_progressive_config(model_name):
    """使用渐进式配置加载模型"""
    configs = [
        {
            "name": "标准配置",
            "gpu_memory_utilization": 0.85,
            "max_model_len": 512,
            "enable_prefix_caching": True,
            "tensor_parallel_size": 1,
            "block_size": 16
        },
        {
            "name": "保守配置",
            "gpu_memory_utilization": 0.75,
            "max_model_len": 256,
            "enable_prefix_caching": False,
            "tensor_parallel_size": 1,
            "block_size": 16
        },
        {
            "name": "最小配置",
            "gpu_memory_utilization": 0.65,
            "max_model_len": 128,
            "enable_prefix_caching": False,
            "tensor_parallel_size": 1,
            "block_size": 8
        },
        {
            "name": "超小配置",
            "gpu_memory_utilization": 0.55,
            "max_model_len": 64,
            "enable_prefix_caching": False,
            "tensor_parallel_size": 1,
            "block_size": 8
        },
        {
            "name": "极限配置",
            "gpu_memory_utilization": 0.45,
            "max_model_len": 32,
            "enable_prefix_caching": False,
            "tensor_parallel_size": 1,
            "block_size": 4
        }
    ]
    
    for config in configs:
        print(f"=== 尝试{config['name']} ===")
        try:
            llm = LLM(
                model=model_name,
                tensor_parallel_size=config["tensor_parallel_size"],
                max_model_len=config["max_model_len"],
                enable_prefix_caching=config["enable_prefix_caching"],
                trust_remote_code=True,
                gpu_memory_utilization=config["gpu_memory_utilization"],
                block_size=config["block_size"],
                enforce_eager=True,  # 禁用CUDA图以节省内存
                disable_custom_all_reduce=True  # 禁用自定义all-reduce
            )
            print(f"✅ {config['name']}加载成功！")
            return llm
        except Exception as e:
            print(f"❌ {config['name']}失败: {e}")
            clear_gpu_memory()
            continue
    
    print("❌ 所有配置都失败了")
    return None

def main():
    """主函数"""
    print("GPU内存调试工具")
    print("=" * 50)
    
    # 检查系统状态
    check_system_memory()
    check_gpu_memory()
    check_gpu_processes()
    
    # 清理内存
    clear_gpu_memory()
    
    # 模型路径
    model_name = "/home/<USER>/Qwen-QwQ-32B-W8A8-SmoothQuant"
    
    # 尝试加载模型
    llm = load_model_with_progressive_config(model_name)
    
    if llm:
        print("=== 测试推理 ===")
        prompts = ["Hello, how are you?"]
        sampling_params = SamplingParams(
            temperature=0.0,
            top_p=1.0,
            max_tokens=20
        )
        
        try:
            outputs = llm.generate(prompts, sampling_params)
            for output in outputs:
                print(f"Prompt: {output.prompt}")
                print(f"Generated: {output.outputs[0].text}")
        except Exception as e:
            print(f"推理失败: {e}")
    
    # 最终内存状态
    print("\n=== 最终GPU内存状态 ===")
    check_gpu_memory()

if __name__ == "__main__":
    main()
