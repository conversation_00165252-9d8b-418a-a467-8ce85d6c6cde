# vLLM移植检查清单与问题排查指南

## 1. 移植前准备检查清单

### 1.1 硬件环境评估 ✅
- [ ] **计算能力评估**
  - INT8矩阵乘法性能 (目标: >50 TOPS)
  - 内存带宽 (目标: >500 GB/s)
  - 并行计算单元数量
  
- [ ] **内存容量规划**
  - 模型权重存储: ~16GB (32B模型的INT8权重)
  - KV缓存: 根据max_seq_len和batch_size计算
  - 工作内存: ~4-8GB
  
- [ ] **软件依赖检查**
  - C++17编译器支持
  - PyTorch >= 2.0
  - OpenMP或其他并行库
  - 自有硬件的SDK/驱动

### 1.2 vLLM源码分析 ✅
- [ ] **关键文件识别**
  - `vllm/_custom_ops/` - CUDA算子实现
  - `vllm/model_executor/layers/quantization/` - 量化层实现
  - `vllm/platforms/` - 硬件平台抽象
  
- [ ] **依赖关系梳理**
  - CUTLASS依赖点: `cutlass_scaled_mm`
  - PyTorch依赖点: `torch._scaled_mm`
  - 硬件检测点: `cutlass_fp8_supported()`

## 2. 核心算子移植检查清单

### 2.1 量化GEMM算子 🔥
- [ ] **接口兼容性**
  ```cpp
  // 必须实现的接口签名
  torch::Tensor scaled_mm(
      const torch::Tensor& qA,        // INT8 [M, K]
      const torch::Tensor& qB,        // INT8 [K, N]
      const torch::Tensor& scale_a,   // FP32 [M, 1] 或 [1]
      const torch::Tensor& scale_b,   // FP32 [1, N] 或 [1]
      torch::ScalarType out_dtype,    // 通常是torch::kFloat16
      const torch::Tensor& bias = {}  // FP32 [N] (可选)
  );
  ```

- [ ] **性能基准**
  - 小矩阵 (128×512×256): < 0.1ms
  - 中等矩阵 (512×2048×1024): < 1ms
  - 大矩阵 (1024×4096×2048): < 5ms

- [ ] **精度验证**
  - 与FP16参考实现误差 < 1%
  - 支持per-token和per-tensor缩放
  - 数值稳定性检查 (无NaN/Inf)

### 2.2 动态激活量化算子 🔥
- [ ] **接口兼容性**
  ```cpp
  std::pair<torch::Tensor, torch::Tensor> scaled_fp8_quant(
      const torch::Tensor& input,           // FP16 [batch*seq, hidden]
      const torch::Tensor& input_scale,     // FP32 (可选)
      bool use_per_token_if_dynamic = true, // per-token量化
      int64_t num_token_padding = 0         // padding数量
  );
  ```

- [ ] **功能验证**
  - Per-token量化: 每行独立缩放
  - Per-tensor量化: 全局缩放
  - 静态量化: 使用预定义缩放因子
  - 输出范围: INT8 [-128, 127]

### 2.3 硬件抽象层适配 🔧
- [ ] **平台检测函数**
  ```python
  def cutlass_fp8_supported() -> bool:
      return True  # 根据硬件能力返回
  
  def current_platform.has_device_capability(cap: int) -> bool:
      return custom_hardware.supports_capability(cap)
  ```

- [ ] **算子分发逻辑**
  ```python
  def dispatch_w8a8_scaled_mm(...):
      # 始终返回自定义实现
      return custom_quantized_ops.scaled_mm
  ```

## 3. 集成测试检查清单

### 3.1 单元测试 ✅
- [ ] **量化GEMM测试**
  - 不同矩阵尺寸 (小/中/大)
  - 不同缩放模式 (per-token/per-tensor)
  - 边界条件 (零值、极值)
  - 内存对齐测试

- [ ] **激活量化测试**
  - 不同输入分布 (正态/均匀/稀疏)
  - 不同序列长度
  - 批处理测试
  - 数值精度验证

### 3.2 集成测试 ✅
- [ ] **模型加载测试**
  - 权重正确加载
  - 缩放因子正确设置
  - 内存分配成功
  - 配置参数解析

- [ ] **端到端推理测试**
  - 单序列推理
  - 批量推理
  - 长序列处理
  - 多轮对话

### 3.3 性能测试 ✅
- [ ] **延迟测试**
  - First token latency < 100ms
  - Token generation rate > 50 tokens/s
  - 内存使用 < 预期值的120%

- [ ] **吞吐量测试**
  - 并发请求处理
  - 批处理效率
  - 资源利用率

## 4. 常见问题排查指南

### 4.1 编译问题 🔧

#### 问题1: PyTorch扩展编译失败
```bash
# 错误信息
error: 'torch::kInt8' was not declared in this scope

# 解决方案
# 1. 检查PyTorch版本
python -c "import torch; print(torch.__version__)"

# 2. 更新include路径
#include <torch/torch.h>
#include <torch/extension.h>

# 3. 检查编译标志
-DTORCH_EXTENSION_NAME=custom_quantized_ops
```

#### 问题2: OpenMP链接错误
```bash
# 错误信息
undefined reference to `omp_get_thread_num'

# 解决方案
# 1. 安装OpenMP
sudo apt-get install libomp-dev

# 2. 添加编译标志
-fopenmp

# 3. 添加链接标志
-lgomp
```

### 4.2 运行时问题 🔧

#### 问题3: 算子未找到错误
```python
# 错误信息
AttributeError: module 'torch.ops.vllm' has no attribute 'cutlass_scaled_mm'

# 解决方案
# 1. 确保算子正确注册
import custom_quantized_ops
torch.ops.load_library("path/to/libcustom_quantized_ops.so")

# 2. 检查符号导出
nm -D libcustom_quantized_ops.so | grep scaled_mm

# 3. 验证Python绑定
python -c "import custom_quantized_ops; print(dir(custom_quantized_ops))"
```

#### 问题4: 精度损失过大
```python
# 错误现象
Max error: 0.15 (超过0.1阈值)

# 排查步骤
# 1. 检查量化实现
def debug_quantization(input_tensor):
    qinput, scale = scaled_fp8_quant(input_tensor)
    dequant = qinput.float() * scale
    error = torch.abs(input_tensor - dequant)
    print(f"Quantization error: {torch.max(error)}")

# 2. 检查GEMM实现
def debug_gemm(qA, qB, scale_a, scale_b):
    # 对比参考实现
    ref_result = torch.matmul(qA.float(), qB.float()) * scale_a * scale_b
    our_result = custom_scaled_mm(qA, qB, scale_a, scale_b)
    print(f"GEMM error: {torch.max(torch.abs(ref_result - our_result))}")

# 3. 数值稳定性检查
def check_numerical_stability(tensor):
    has_nan = torch.any(torch.isnan(tensor))
    has_inf = torch.any(torch.isinf(tensor))
    print(f"NaN: {has_nan}, Inf: {has_inf}")
```

#### 问题5: 性能不达标
```python
# 性能分析工具
import time
import torch.profiler

def profile_quantized_gemm():
    with torch.profiler.profile(
        activities=[torch.profiler.ProfilerActivity.CPU],
        record_shapes=True
    ) as prof:
        # 执行量化GEMM
        result = custom_scaled_mm(qA, qB, scale_a, scale_b)
    
    print(prof.key_averages().table(sort_by="cpu_time_total"))

# 性能优化检查点
# 1. 内存访问模式
# 2. 并行度设置
# 3. 缓存命中率
# 4. 算法复杂度
```

### 4.3 内存问题 🔧

#### 问题6: 内存不足
```python
# 错误信息
RuntimeError: CUDA out of memory

# 解决方案
# 1. 降低GPU内存利用率
llm = LLM(model="...", gpu_memory_utilization=0.6)

# 2. 减少序列长度
llm = LLM(model="...", max_model_len=512)

# 3. 启用CPU offload
llm = LLM(model="...", cpu_offload_gb=4.0)

# 4. 检查内存泄漏
import psutil
process = psutil.Process()
print(f"Memory usage: {process.memory_info().rss / 1024**3:.2f} GB")
```

#### 问题7: 内存碎片化
```cpp
// 内存池优化
class MemoryPool {
    std::vector<void*> free_blocks_;
    std::mutex pool_mutex_;
    
public:
    void* allocate(size_t size) {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        // 查找合适大小的块
        for (auto& block : free_blocks_) {
            if (get_block_size(block) >= size) {
                free_blocks_.erase(block);
                return block;
            }
        }
        // 分配新块
        return malloc(size);
    }
    
    void deallocate(void* ptr) {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        free_blocks_.push_back(ptr);
    }
};
```

## 5. 性能优化建议

### 5.1 计算优化 ⚡
- **SIMD指令**: 使用AVX-512或NEON指令集
- **循环展开**: 减少分支预测开销
- **内存预取**: 提前加载数据到缓存
- **算子融合**: 减少中间结果存储

### 5.2 内存优化 💾
- **权重压缩**: 使用稀疏存储格式
- **缓存策略**: LRU缓存热点权重
- **内存对齐**: 确保数据对齐到缓存行
- **异步传输**: 重叠计算和数据传输

### 5.3 并行优化 🔄
- **线程池**: 复用线程减少创建开销
- **任务分割**: 平衡负载分布
- **锁优化**: 减少同步开销
- **NUMA感知**: 优化多socket系统

## 6. 验收标准

### 6.1 功能验收 ✅
- [ ] 所有单元测试通过
- [ ] 端到端推理成功
- [ ] 精度误差 < 1%
- [ ] 支持所有量化模式

### 6.2 性能验收 ⚡
- [ ] 推理延迟 < 原生vLLM的150%
- [ ] 内存使用 < 原生vLLM的120%
- [ ] 吞吐量 > 原生vLLM的80%
- [ ] 资源利用率 > 70%

### 6.3 稳定性验收 🛡️
- [ ] 连续运行24小时无崩溃
- [ ] 内存无泄漏
- [ ] 多并发请求稳定
- [ ] 异常情况正确处理

这个检查清单和排查指南为你的vLLM移植工作提供了系统性的质量保证和问题解决方案。
