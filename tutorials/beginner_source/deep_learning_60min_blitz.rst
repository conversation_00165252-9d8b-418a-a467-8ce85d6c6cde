Deep Learning with <PERSON><PERSON><PERSON>orch: A 60 Minute Blitz
---------------------------------------------
**Author**: `<PERSON><PERSON><PERSON> Chintala <http://soumith.ch>`_

.. raw:: html

   <div style="margin-top:10px; margin-bottom:10px;">
     <iframe width="560" height="315" src="https://www.youtube.com/embed/u7x8RXwLKcA" frameborder="0" allow="accelerometer; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
   </div>

Goal of this tutorial:

-  Understand PyTorch’s Tensor library and neural networks at a high
   level.
-  Train a small neural network to classify images

*This tutorial assumes that you have a basic familiarity of numpy*

.. Note::
    Make sure you have the `torch`_ and `torchvision`_ packages installed.

.. _torch: https://github.com/pytorch/pytorch
.. _torchvision: https://github.com/pytorch/vision


.. toctree::
   :hidden:

   /beginner/blitz/tensor_tutorial
   /beginner/blitz/autograd_tutorial
   /beginner/blitz/neural_networks_tutorial
   /beginner/blitz/cifar10_tutorial

.. galleryitem:: /beginner/blitz/tensor_tutorial.py
    :figure: /_static/img/tensor_illustration_flat.png

.. galleryitem:: /beginner/blitz/autograd_tutorial.py
    :figure: /_static/img/autodiff.png

.. galleryitem:: /beginner/blitz/neural_networks_tutorial.py
    :figure: /_static/img/mnist.png

.. galleryitem:: /beginner/blitz/cifar10_tutorial.py
    :figure: /_static/img/cifar10.png

.. raw:: html

    <div style='clear:both'></div>
