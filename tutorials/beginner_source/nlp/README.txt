Deep Learning for NLP with P<PERSON>orch
----------------------------------

1. pytorch_tutorial.py
	Introduction to PyTorch
	https://pytorch.org/tutorials/beginner/nlp/pytorch_tutorial.html

2. deep_learning_tutorial.py
	Deep Learning with PyTorch
	https://pytorch.org/tutorials/beginner/nlp/deep_learning_tutorial.html

3. word_embeddings_tutorial.py
	Word Embeddings: Encoding Lexical Semantics
	https://pytorch.org/tutorials/beginner/nlp/word_embeddings_tutorial.html

4. sequence_models_tutorial.py
	Sequence Models and Long-Short Term Memory Networks
	https://pytorch.org/tutorials/beginner/nlp/sequence_models_tutorial.html

5. advanced_tutorial.py
	Advanced: Making Dynamic Decisions and the Bi-LSTM CRF
	https://pytorch.org/tutorials/beginner/nlp/advanced_tutorial.html