PyTorch for Former Torch Users
------------------------------
**Author**: `Soumith Chintala <http://soumith.ch>`_

In this tutorial, you will learn the following:

1. Using torch Tensors, and important difference against (Lua)Torch
2. Using the autograd package
3. Building neural networks

  -  Building a ConvNet
  -  Building a Recurrent Net

4. Use multiple GPUs


.. toctree::
   :hidden:

   /beginner/former_torchies/tensor_tutorial_old
   /beginner/former_torchies/autograd_tutorial_old
   /beginner/former_torchies/nnft_tutorial
   /beginner/former_torchies/parallelism_tutorial

.. galleryitem:: /beginner/former_torchies/tensor_tutorial_old.py
    :figure: /_static/img/tensor_illustration_flat.png

.. galleryitem:: /beginner/former_torchies/autograd_tutorial_old.py

.. galleryitem:: /beginner/former_torchies/nnft_tutorial.py
    :figure: /_static/img/torch-nn-vs-pytorch-nn.png

.. galleryitem:: /beginner/former_torchies/parallelism_tutorial.py

.. raw:: html

    <div style='clear:both'></div>
