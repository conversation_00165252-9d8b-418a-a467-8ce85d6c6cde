version: 2.1

executors:
  windows-with-nvidia-gpu:
    machine:
      resource_class: windows.gpu.nvidia.medium
      image: windows-server-2019-nvidia:stable
      shell: bash.exe

install_official_git_client: &install_official_git_client
  name: Install Official Git Client
  no_output_timeout: "1h"
  command: |
    set -e
    sudo apt-get -qq update
    sudo apt-get -qq install openssh-client git

# This system setup script is meant to run before the CI-related scripts, e.g.,
# installing Git client, checking out code, setting up CI env, and
# building/testing.
setup_linux_system_environment: &setup_linux_system_environment
  name: Set Up System Environment
  no_output_timeout: "1h"
  command: |
    set -ex

    # Set up CircleCI GPG keys for apt, if needed
    curl -L https://packagecloud.io/circleci/trusty/gpgkey | sudo apt-key add -

    # Stop background apt updates.  Hypothetically, the kill should not
    # be necessary, because stop is supposed to send a kill signal to
    # the process, but we've added it for good luck.  Also
    # hypothetically, it's supposed to be unnecessary to wait for
    # the process to block.  We also have that line for good luck.
    # If you like, try deleting them and seeing if it works.
    sudo systemctl stop apt-daily.service || true
    sudo systemctl kill --kill-who=all apt-daily.service || true

    sudo systemctl stop unattended-upgrades.service || true
    sudo systemctl kill --kill-who=all unattended-upgrades.service || true

    # wait until `apt-get update` has been killed
    while systemctl is-active --quiet apt-daily.service
    do
      sleep 1;
    done
    while systemctl is-active --quiet unattended-upgrades.service
    do
      sleep 1;
    done

    # See if we actually were successful
    systemctl list-units --all | cat

    sudo apt-get purge -y unattended-upgrades

    cat /etc/apt/sources.list

    ps auxfww | grep [a]pt
    ps auxfww | grep dpkg

pytorch_tutorial_build_defaults: &pytorch_tutorial_build_defaults
  machine:
    image: ubuntu-1604:201903-01
  steps:
  - checkout
  - run:
      <<: *setup_linux_system_environment
  - run:
      name: Set Up CI Environment
      no_output_timeout: "1h"
      command: |
        set -e

        # Set up NVIDIA docker repo
        curl -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
        echo "deb https://nvidia.github.io/libnvidia-container/ubuntu16.04/amd64 /" | sudo tee -a /etc/apt/sources.list.d/nvidia-docker.list
        echo "deb https://nvidia.github.io/nvidia-container-runtime/ubuntu16.04/amd64 /" | sudo tee -a /etc/apt/sources.list.d/nvidia-docker.list
        echo "deb https://nvidia.github.io/nvidia-docker/ubuntu16.04/amd64 /" | sudo tee -a /etc/apt/sources.list.d/nvidia-docker.list

        sudo apt-get -y update
        sudo apt-get -y remove linux-image-generic linux-headers-generic linux-generic docker-ce
        # WARNING: Docker version is hardcoded here; you must update the
        # version number below for docker-ce and nvidia-docker2 to get newer
        # versions of Docker.  We hardcode these numbers because we kept
        # getting broken CI when Docker would update their docker version,
        # and nvidia-docker2 would be out of date for a day until they
        # released a newer version of their package.
        #
        # How to figure out what the correct versions of these packages are?
        # My preferred method is to start a Docker instance of the correct
        # Ubuntu version (e.g., docker run -it ubuntu:16.04) and then ask
        # apt what the packages you need are.  Note that the CircleCI image
        # comes with Docker.
        sudo apt-get -y install \
          linux-headers-$(uname -r) \
          linux-image-generic \
          moreutils \
          docker-ce=5:18.09.4~3-0~ubuntu-xenial \
          nvidia-container-runtime=2.0.0+docker18.09.4-1 \
          nvidia-docker2=2.0.3+docker18.09.4-1 \
          expect-dev

        sudo pkill -SIGHUP dockerd

        sudo pip -q install awscli==1.16.35

        if [ -n "${CUDA_VERSION}" ]; then
          DRIVER_FN="NVIDIA-Linux-x86_64-440.59.run"
          wget "https://s3.amazonaws.com/ossci-linux/nvidia_driver/$DRIVER_FN"
          sudo /bin/bash "$DRIVER_FN" -s --no-drm || (sudo cat /var/log/nvidia-installer.log && false)
          nvidia-smi
        fi

        # This IAM user only allows read-write access to ECR
        export AWS_ACCESS_KEY_ID=${CIRCLECI_AWS_ACCESS_KEY_FOR_ECR_READ_ONLY}
        export AWS_SECRET_ACCESS_KEY=${CIRCLECI_AWS_SECRET_KEY_FOR_ECR_READ_ONLY}
        eval $(aws ecr get-login --region us-east-1 --no-include-email)
  - run:
      name: Build
      no_output_timeout: "20h"
      command: |
        set -e

        export pyTorchDockerImageTag=291
        echo "PyTorchDockerImageTag: "${pyTorchDockerImageTag}

        cat >/home/<USER>/project/ci_build_script.sh \<<EOL
        # =================== The following code will be executed inside Docker container ===================
        set -ex

        .jenkins/build.sh
        # =================== The above code will be executed inside Docker container ===================
        EOL
        chmod +x /home/<USER>/project/ci_build_script.sh

        export DOCKER_IMAGE=${DOCKER_IMAGE}:${pyTorchDockerImageTag}
        echo "DOCKER_IMAGE: "${DOCKER_IMAGE}
        docker pull ${DOCKER_IMAGE} >/dev/null
        if [ -n "${CUDA_VERSION}" ]; then
          export id=$(docker run --runtime=nvidia -t -d -w /var/lib/jenkins ${DOCKER_IMAGE})
        else
          export id=$(docker run -t -d -w /var/lib/jenkins ${DOCKER_IMAGE})
        fi

        echo "declare -x JOB_BASE_NAME=${CIRCLE_JOB}" > /home/<USER>/project/env
        echo "declare -x COMMIT_ID=${CIRCLE_SHA1}" >> /home/<USER>/project/env
        echo "declare -x COMMIT_SOURCE=${CIRCLE_BRANCH}" >> /home/<USER>/project/env
        # DANGER! DO NOT REMOVE THE `set +x` SETTING HERE!
        set +x
        if [[ "$CIRCLE_BRANCH" == master ]]; then
          if [ -z "${CIRCLECI_AWS_ACCESS_KEY_FOR_PYTORCH_TUTORIAL_BUILD_MASTER_S3_BUCKET}" ]; then exit 1; fi
          if [ -z "${CIRCLECI_AWS_SECRET_KEY_FOR_PYTORCH_TUTORIAL_BUILD_MASTER_S3_BUCKET}" ]; then exit 1; fi
          if [ -z "${GITHUB_PYTORCHBOT_USERNAME}" ]; then exit 1; fi
          if [ -z "${GITHUB_PYTORCHBOT_TOKEN}" ]; then exit 1; fi

          echo "declare -x AWS_ACCESS_KEY_ID=${CIRCLECI_AWS_ACCESS_KEY_FOR_PYTORCH_TUTORIAL_BUILD_MASTER_S3_BUCKET}" >> /home/<USER>/project/env
          echo "declare -x AWS_SECRET_ACCESS_KEY=${CIRCLECI_AWS_SECRET_KEY_FOR_PYTORCH_TUTORIAL_BUILD_MASTER_S3_BUCKET}" >> /home/<USER>/project/env
          echo "declare -x GITHUB_PYTORCHBOT_USERNAME=${GITHUB_PYTORCHBOT_USERNAME}" >> /home/<USER>/project/env
          echo "declare -x GITHUB_PYTORCHBOT_TOKEN=${GITHUB_PYTORCHBOT_TOKEN}" >> /home/<USER>/project/env
        else
          echo "declare -x AWS_ACCESS_KEY_ID=${CIRCLECI_AWS_ACCESS_KEY_FOR_PYTORCH_TUTORIAL_BUILD_PR_S3_BUCKET}" >> /home/<USER>/project/env
          echo "declare -x AWS_SECRET_ACCESS_KEY=${CIRCLECI_AWS_SECRET_KEY_FOR_PYTORCH_TUTORIAL_BUILD_PR_S3_BUCKET}" >> /home/<USER>/project/env
        fi
        set -x

        docker cp /home/<USER>/project/. "$id:/var/lib/jenkins/workspace"

        export COMMAND='((echo "source ./workspace/env" && echo "sudo chown -R jenkins workspace && cd workspace && ./ci_build_script.sh") | docker exec -u jenkins -i "$id" bash) 2>&1'
        echo ${COMMAND} > ./command.sh && unbuffer bash ./command.sh | ts

pytorch_tutorial_build_worker_defaults: &pytorch_tutorial_build_worker_defaults
  environment:
    DOCKER_IMAGE: "308535385114.dkr.ecr.us-east-1.amazonaws.com/pytorch/pytorch-linux-xenial-cuda9-cudnn7-py3"
    CUDA_VERSION: "9"
  resource_class: gpu.medium
  <<: *pytorch_tutorial_build_defaults

pytorch_tutorial_build_manager_defaults: &pytorch_tutorial_build_manager_defaults
  environment:
    DOCKER_IMAGE: "308535385114.dkr.ecr.us-east-1.amazonaws.com/pytorch/pytorch-linux-xenial-cuda9-cudnn7-py3"
  resource_class: medium
  <<: *pytorch_tutorial_build_defaults

pytorch_windows_build_worker: &pytorch_windows_build_worker
  executor: windows-with-nvidia-gpu
  steps:
    - checkout
    - run:
        name: Install Cuda
        no_output_timeout: 30m
        command: |
          .circleci/scripts/windows_cuda_install.sh
    - run:
        name: Generate cache key
        # This will refresh cache on Sundays, build should generate new cache.
        command: echo "$(date +"%Y-%U")" > .circleci-weekly
    - restore_cache:
        keys:
          - data-{{ checksum "Makefile" }}-{{ checksum ".circleci-weekly" }}
    - run:
        name: test
        no_output_timeout: "1h"
        command: |
          .circleci/scripts/build_for_windows.sh
    - save_cache:
        key: data-{{ checksum "Makefile" }}-{{ checksum ".circleci-weekly" }}
        paths:
          - advanced_source/data
          - beginner_source/data
          - intermediate_source/data
          - prototype_source/data

jobs:
  pytorch_tutorial_pr_build_worker_0:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_1:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_2:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_3:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_4:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_5:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_6:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_7:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_8:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_9:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_10:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_11:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_12:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_13:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_14:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_15:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_16:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_17:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_18:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_worker_19:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_pr_build_manager:
    <<: *pytorch_tutorial_build_manager_defaults

  pytorch_tutorial_master_build_worker_0:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_1:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_2:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_3:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_4:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_5:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_6:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_7:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_8:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_9:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_10:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_11:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_12:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_13:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_14:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_15:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_16:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_17:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_18:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_worker_19:
    <<: *pytorch_tutorial_build_worker_defaults

  pytorch_tutorial_master_build_manager:
    <<: *pytorch_tutorial_build_manager_defaults

  pytorch_tutorial_windows_pr_build_worker_0:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_pr_build_worker_1:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_pr_build_worker_2:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_pr_build_worker_3:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_master_build_worker_0:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_master_build_worker_1:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_master_build_worker_2:
    <<: *pytorch_windows_build_worker

  pytorch_tutorial_windows_master_build_worker_3:
    <<: *pytorch_windows_build_worker

workflows:
  build:
    jobs:
      # Build jobs that only run on PR
      - pytorch_tutorial_pr_build_worker_0:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_1:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_2:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_3:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_4:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_5:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_6:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_7:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_8:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_9:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_10:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_11:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_12:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_13:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_14:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_15:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_16:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_17:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_18:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_worker_19:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_pr_build_manager:
          filters:
            branches:
              ignore:
                - master
      # Build jobs that only run on master
      - pytorch_tutorial_master_build_worker_0:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_1:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_2:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_3:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_4:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_5:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_6:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_7:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_8:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_9:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_10:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_11:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_12:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_13:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_14:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_15:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_16:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_17:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_18:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_worker_19:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_master_build_manager:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_windows_pr_build_worker_0:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_windows_pr_build_worker_1:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_windows_pr_build_worker_2:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_windows_pr_build_worker_3:
          filters:
            branches:
              ignore:
                - master
      - pytorch_tutorial_windows_master_build_worker_0:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_windows_master_build_worker_1:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_windows_master_build_worker_2:
          context: org-member
          filters:
            branches:
              only:
                - master
      - pytorch_tutorial_windows_master_build_worker_3:
          context: org-member
          filters:
            branches:
              only:
                - master

