# Refer to ./jenkins/build.sh for tutorial build instructions

sphinx==1.8.2
sphinx-gallery==0.3.1
sphinx-copybutton
tqdm
numpy
matplotlib
torch
torchvision
torchtext
torchaudio
PyHamcrest
bs4
awscli==1.16.35
flask
spacy
ray[tune]

# PyTorch Theme
-e git+git://github.com/pytorch/pytorch_sphinx_theme.git#egg=pytorch_sphinx_theme

ipython

# to run examples
pandas
scikit-image
# pillow >= 4.2 will throw error when trying to write mode RGBA as JPEG,
# this is a workaround to the issue.
pillow==7.1.0
wget
