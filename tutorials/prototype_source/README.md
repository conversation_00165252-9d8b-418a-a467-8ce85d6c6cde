# Prototype Tutorials and Recipes

This directory contains tutorials and recipes demonstrating prototype features in PyTorch. 

**Prototype features** are not available as part of binary distributions like PyPI or Conda (except maybe behind run-time flags). To test these features we would, depending on the feature, recommend building from master or using the nightly wheelss that are made available on pytorch.org. 

These are intentionally left out of the pytorch.org/tutorials build and will not show up on the website.

*Level of commitment:* We are committing to gathering high bandwidth feedback only on these features. Based on this feedback and potential further engagement between community members, we as a community will decide if we want to upgrade the level of commitment or to fail fast. 
