Prototype Tutorials
------------------
1. distributed_rpc_profiling.rst
     Profiling PyTorch RPC-Based Workloads
     https://github.com/pytorch/tutorials/blob/release/1.6/prototype_source/distributed_rpc_profiling.rst

2. graph_mode_static_quantization_tutorial.py
	   Graph Mode Post Training Static Quantization in PyTorch
	   https://pytorch.org/tutorials/prototype/graph_mode_static_quantization_tutorial.html
	   
3. graph_mode_dynamic_bert_tutorial.rst
	   Graph Mode Dynamic Quantization on BERT
	   https://github.com/pytorch/tutorials/blob/master/prototype_source/graph_mode_dynamic_bert_tutorial.rst

4. numeric_suite_tutorial.py
	   PyTorch Numeric Suite Tutorial
	   https://github.com/pytorch/tutorials/blob/master/prototype_source/numeric_suite_tutorial.py

5. torchscript_freezing.py
	   Model Freezing in TorchScript
	   https://github.com/pytorch/tutorials/blob/master/prototype_source/torchscript_freezing.py

6. vulkan_workflow.rst
     Vulkan Backend User Workflow
     https://pytorch.org/tutorials/intermediate/vulkan_workflow.html
