{% extends "!layout.html" %}

{% block footer %}
{{ super() }}
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-117752657-2"></script>

<script>

  window.dataLayer = window.dataLayer || [];

  function gtag(){dataLayer.push(arguments);}

  gtag('js', new Date());
  gtag('config', 'UA-117752657-2');

</script>

<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window,document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '243028289693773');
  fbq('track', 'PageView');

  $("[data-behavior='call-to-action-event']").on('click', function(){
    fbq('trackCustom', "Download", {
      tutorialTitle: $('h1:first').text(),
      downloadLink: this.href,
      tutorialLink: window.location.href,
      downloadTitle: $(this).attr("data-response")
    });

    gtag('event', 'click', {
      'event_category': 'Download',
      'event_label': $(this).attr("data-response")
    });

    gtag('event', 'click', {
      'event_category': $(this).attr("data-response"),
      'event_label': $("h1").first().text(),
      'tutorial_link': window.location.href
    });
   });

   $("[data-behavior='was-this-helpful-event']").on('click', function(){
    $(".helpful-question").hide();
    $(".was-helpful-thank-you").show();

    fbq('trackCustom', "Was this Helpful?", {
      tutorialLink: window.location.href,
      tutorialTitle: $('h1:first').text(),
      helpful: $(this).attr("data-response")
    });

    gtag('event', $(this).attr("data-response"), {
      'event_category': 'Was this Helpful?',
      'event_label': $(this).attr("data-response")
    });

    gtag('event', $(this).attr("data-response"), {
      'event_category': 'Was this Helpful?',
      'event_label': $("h1").first().text()
    });
   });

   if (location.pathname == "/") {
     $(".helpful-container").hide();
     $(".hr-bottom").hide();
   }
</script>

<noscript>
  <img height="1" width="1"
  src="https://www.facebook.com/tr?id=243028289693773&ev=PageView
  &noscript=1"/>
</noscript>
<img height="1" width="1" style="border-style:none;" alt="" src="https://www.googleadservices.com/pagead/conversion/795629140/?label=txkmCPmdtosBENSssfsC&amp;guid=ON&amp;script=0"/>
{% endblock %}
