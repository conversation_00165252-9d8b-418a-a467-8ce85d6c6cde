digraph G {

    // Main styles
    nodesep=0.3; ranksep=0.15;

    node [shape=rect, fillcolor=darkorange, color=white, style=filled, fontsize=11, fontname="arial", height=0.2];
    edge [color=gray, arrowsize=0.5];

    // Layout
    {rank=same;input;prev_hidden}

    input -> embedding;
    embedding -> embedded;
    embedded -> gru;
    prev_hidden -> gru;
    gru -> output;
    gru -> hidden;

    embedding [fillcolor=dodgerblue, fontcolor=white];
    gru [fillcolor=dodgerblue, fontcolor=white];

}
