Intermediate tutorials
----------------------

1. tensorboard_tutorial.py
	Classifying Names with a Character-Level RNN
	https://pytorch.org/tutorials/beginner/tensorboard_tutorial.html

2. char_rnn_classification_tutorial.py
	Classifying Names with a Character-Level RNN
	https://pytorch.org/tutorials/intermediate/char_rnn_classification_tutorial.html

3. char_rnn_generation_tutorial.py
	Generating Names with a Character-Level RNN
	https://pytorch.org/tutorials/intermediate/char_rnn_generation_tutorial.html

4. seq2seq_translation_tutorial.py
	Translation with a Sequence to Sequence Network and Attention
	https://pytorch.org/tutorials/intermediate/seq2seq_translation_tutorial.html

5. reinforcement_q_learning.py
	Reinforcement Learning (DQN) Tutorial
	https://pytorch.org/tutorials/intermediate/reinforcement_q_learning.html

6. dist_tuto.rst
	Writing Distributed Applications with PyTorch
	https://pytorch.org/tutorials/intermediate/dist_tuto.html

7. spatial_transformer_tutorial
	Spatial Transformer Networks Tutorial
	https://pytorch.org/tutorials/intermediate/spatial_transformer_tutorial.html

8. flask_rest_api_tutorial.py
	Deploying PyTorch and Building a REST API using Flask
	https://pytorch.org/tutorials/beginner/flask_rest_api_tutorial.html
