#!/usr/bin/env python3
"""
快速检查GPU状态的脚本
"""

import torch
import subprocess

def main():
    print("=== GPU状态检查 ===")
    
    # 检查CUDA可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    # 检查GPU数量
    gpu_count = torch.cuda.device_count()
    print(f"🔍 检测到 {gpu_count} 张GPU")
    
    # 检查每张GPU的详细信息
    available_gpus = []
    total_free_memory = 0
    
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        
        # 清理并检查内存
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
        
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        free_memory = total_memory - allocated
        
        print(f"\n🎯 GPU {i}: {props.name}")
        print(f"   总内存: {total_memory:.2f} GB")
        print(f"   已分配: {allocated:.2f} GB")
        print(f"   已保留: {reserved:.2f} GB")
        print(f"   可用内存: {free_memory:.2f} GB")
        print(f"   计算能力: {props.major}.{props.minor}")
        
        if free_memory > 8.0:  # 至少8GB可用内存
            available_gpus.append(i)
            total_free_memory += free_memory
            print(f"   ✅ 可用于模型加载")
        else:
            print(f"   ⚠️ 内存不足")
    
    print(f"\n📊 总结:")
    print(f"   可用GPU数量: {len(available_gpus)}")
    print(f"   可用GPU编号: {available_gpus}")
    print(f"   总可用内存: {total_free_memory:.2f} GB")
    
    # 推荐配置
    if len(available_gpus) >= 4:
        print(f"\n💡 推荐配置: 使用4张GPU (tensor_parallel_size=4)")
    elif len(available_gpus) >= 2:
        print(f"\n💡 推荐配置: 使用{len(available_gpus)}张GPU (tensor_parallel_size={len(available_gpus)})")
    elif len(available_gpus) == 1:
        print(f"\n💡 推荐配置: 使用单GPU (tensor_parallel_size=1)")
    else:
        print(f"\n❌ 没有足够的GPU内存可用")
        return
    
    # 显示nvidia-smi信息
    print(f"\n=== nvidia-smi 输出 ===")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        print(result.stdout)
    except FileNotFoundError:
        print("nvidia-smi 命令不可用")

if __name__ == "__main__":
    main()
