#!/usr/bin/env python3
"""
vLLM CUDA Device-Side Assert 错误解决方案
通过设置环境变量和使用eager模式来避免flash_attn相关问题
"""

import os
import torch
import logging

# 设置CUDA调试环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA调用
os.environ['TORCH_USE_CUDA_DSA'] = '1'    # 启用设备端断言
os.environ['NCCL_DEBUG'] = 'INFO'         # NCCL调试信息

# 禁用flash_attn相关功能，使用默认的attention backend
# 不设置VLLM_ATTENTION_BACKEND，让vLLM自动选择合适的后端
os.environ['VLLM_USE_TRITON_FLASH_ATTN'] = '0'       # 禁用Triton Flash Attention
os.environ['VLLM_FLASH_ATTN_CHUNK_SIZE'] = '1'       # 设置最小chunk size

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_cuda_environment():
    """检查CUDA环境"""
    print("=== CUDA环境检查 ===")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    print(f"✅ CUDA版本: {torch.version.cuda}")
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ 可用GPU数量: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        
        print(f"GPU {i}: {props.name}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")
    
    return True

def test_vllm_with_workaround():
    """使用workaround测试vLLM"""
    print("\n=== vLLM Workaround 测试 ===")
    
    try:
        # 延迟导入vLLM，确保环境变量已设置
        from vllm import LLM, SamplingParams
        
        model_name = "/home/<USER>/single_llama"
        
        # 使用最保守的配置，强制使用eager模式
        config = {
            "model": model_name,
            "tensor_parallel_size": 1,
            "gpu_memory_utilization": 0.3,  # 非常保守的内存使用
            "max_model_len": 16,             # 极小的序列长度
            "enable_prefix_caching": False,
            "trust_remote_code": True,
            "block_size": 4,                 # 最小块大小
            "enforce_eager": True,           # 强制使用eager模式，禁用CUDA图
            "disable_custom_all_reduce": True,
            "max_num_seqs": 1,              # 只处理一个序列
            "use_v2_block_manager": False,   # 使用旧版块管理器
        }
        
        print("配置参数:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        print("\n开始加载模型...")
        torch.cuda.empty_cache()
        
        llm = LLM(**config)
        print("✅ 模型加载成功！")
        
        # 极简推理测试
        print("\n开始推理测试...")
        prompts = ["Hi"]
        sampling_params = SamplingParams(
            temperature=0.0,
            max_tokens=1,  # 只生成1个token
            use_beam_search=False
        )
        
        outputs = llm.generate(prompts, sampling_params)
        print("✅ 推理成功！")
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        return True
        
    except Exception as e:
        print(f"❌ vLLM测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_configs():
    """测试多种替代配置"""
    print("\n=== 测试多种配置 ===")
    
    try:
        from vllm import LLM, SamplingParams
        
        model_name = "/home/<USER>/single_llama"
        
        configs = [
            {
                "name": "配置1: 最小化",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.2,
                "max_model_len": 8,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 4,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 1,
                "use_v2_block_manager": False,
            },
            {
                "name": "配置2: 保守",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.3,
                "max_model_len": 16,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 8,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 1,
                "use_v2_block_manager": False,
            },
            {
                "name": "配置3: 中等",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.4,
                "max_model_len": 32,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 16,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 2,
                "use_v2_block_manager": False,
            }
        ]
        
        for config in configs:
            print(f"\n=== 尝试{config['name']} ===")
            
            try:
                torch.cuda.empty_cache()
                
                # 提取配置名称
                config_name = config.pop('name')
                
                llm = LLM(model=model_name, **config)
                print(f"✅ {config_name}加载成功！")
                
                # 简单推理测试
                prompts = ["Hi"]
                sampling_params = SamplingParams(
                    temperature=0.0,
                    max_tokens=1,
                    use_beam_search=False
                )
                
                outputs = llm.generate(prompts, sampling_params)
                print(f"✅ {config_name}推理成功！")
                
                for output in outputs:
                    print(f"输入: {output.prompt}")
                    print(f"输出: {output.outputs[0].text}")
                
                return True
                
            except Exception as e:
                print(f"❌ {config_name}失败: {e}")
                torch.cuda.empty_cache()
                continue
        
        print("❌ 所有配置都失败了")
        return False
        
    except ImportError as e:
        print(f"❌ 无法导入vLLM: {e}")
        return False

def main():
    """主函数"""
    print("vLLM CUDA Device-Side Assert 错误解决方案")
    print("=" * 60)
    
    # 检查CUDA环境
    if not check_cuda_environment():
        print("❌ CUDA环境检查失败，无法继续")
        return False
    
    # 测试基础配置
    if test_vllm_with_workaround():
        print("\n🎉 基础配置测试成功！")
        return True
    
    # 如果基础配置失败，尝试多种配置
    print("\n基础配置失败，尝试其他配置...")
    if test_alternative_configs():
        print("\n🎉 替代配置测试成功！")
        return True
    
    print("\n❌ 所有配置都失败了")
    print("\n建议:")
    print("1. 检查模型文件是否存在且完整")
    print("2. 确保有足够的GPU内存")
    print("3. 尝试重启Python进程")
    print("4. 检查CUDA驱动和PyTorch版本兼容性")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
