<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 685.55859375 6095.921875" style="max-width: 685.55859375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3"><style>#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .error-icon{fill:#a44141;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-thickness-normal{stroke-width:1px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .marker.cross{stroke:lightgrey;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 p{margin:0;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster-label text{fill:#F9FFFE;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster-label span{color:#F9FFFE;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster-label span p{background-color:transparent;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .label text,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 span{fill:#ccc;color:#ccc;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node rect,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node circle,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node ellipse,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node polygon,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .rough-node .label text,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node .label text,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .image-shape .label,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .icon-shape .label{text-anchor:middle;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .rough-node .label,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node .label,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .image-shape .label,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .icon-shape .label{text-align:center;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .node.clickable{cursor:pointer;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .arrowheadPath{fill:lightgrey;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster text{fill:#F9FFFE;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .cluster span{color:#F9FFFE;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 rect.text{fill:none;stroke-width:0;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .icon-shape,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .icon-shape p,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .icon-shape rect,#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M196.012,62L196.012,66.167C196.012,70.333,196.012,78.667,196.012,86.333C196.012,94,196.012,101,196.012,104.5L196.012,108"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M196.012,166L196.012,170.167C196.012,174.333,196.012,182.667,196.012,190.333C196.012,198,196.012,205,196.012,208.5L196.012,212"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M196.012,270L196.012,274.167C196.012,278.333,196.012,286.667,196.012,294.333C196.012,302,196.012,309,196.012,312.5L196.012,316"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M196.012,374L196.012,378.167C196.012,382.333,196.012,390.667,196.012,398.333C196.012,406,196.012,413,196.012,416.5L196.012,420"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M196.012,478L196.012,482.167C196.012,486.333,196.012,494.667,196.012,502.333C196.012,510,196.012,517,196.012,520.5L196.012,524"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M196.012,582L196.012,586.167C196.012,590.333,196.012,598.667,196.012,606.333C196.012,614,196.012,621,196.012,624.5L196.012,628"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M196.012,686L196.012,690.167C196.012,694.333,196.012,702.667,196.012,710.333C196.012,718,196.012,725,196.012,728.5L196.012,732"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M196.012,790L196.012,794.167C196.012,798.333,196.012,806.667,196.012,814.333C196.012,822,196.012,829,196.012,832.5L196.012,836"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M196.012,894L196.012,898.167C196.012,902.333,196.012,910.667,196.012,918.333C196.012,926,196.012,933,196.012,936.5L196.012,940"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M196.012,998L196.012,1002.167C196.012,1006.333,196.012,1014.667,196.012,1022.333C196.012,1030,196.012,1037,196.012,1040.5L196.012,1044"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M196.012,1102L196.012,1106.167C196.012,1110.333,196.012,1118.667,196.012,1126.333C196.012,1134,196.012,1141,196.012,1144.5L196.012,1148"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M196.012,1206L196.012,1210.167C196.012,1214.333,196.012,1222.667,196.012,1230.333C196.012,1238,196.012,1245,196.012,1248.5L196.012,1252"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M196.012,1310L196.012,1314.167C196.012,1318.333,196.012,1326.667,196.082,1334.417C196.152,1342.167,196.293,1349.334,196.363,1352.917L196.433,1356.501"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M165.974,1447.962L153.814,1459.135C141.653,1470.308,117.332,1492.654,105.172,1509.327C93.012,1526,93.012,1537,93.012,1542.5L93.012,1548"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_P_14" d="M227.049,1447.962L239.043,1459.135C251.037,1470.308,275.024,1492.654,287.018,1509.327C299.012,1526,299.012,1537,299.012,1542.5L299.012,1548"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M299.012,1606L299.012,1610.167C299.012,1614.333,299.012,1622.667,299.012,1630.333C299.012,1638,299.012,1645,299.012,1648.5L299.012,1652"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M299.012,1710L299.012,1714.167C299.012,1718.333,299.012,1726.667,299.012,1734.333C299.012,1742,299.012,1749,299.012,1752.5L299.012,1756"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M247.364,1814L239.394,1818.167C231.424,1822.333,215.483,1830.667,207.513,1838.333C199.543,1846,199.543,1853,199.543,1856.5L199.543,1860"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_18" d="M199.543,1918L199.543,1922.167C199.543,1926.333,199.543,1934.667,199.543,1942.333C199.543,1950,199.543,1957,199.543,1960.5L199.543,1964"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_19" d="M199.543,2022L199.543,2026.167C199.543,2030.333,199.543,2038.667,199.543,2046.333C199.543,2054,199.543,2061,199.543,2064.5L199.543,2068"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_20" d="M199.543,2126L199.543,2130.167C199.543,2134.333,199.543,2142.667,199.543,2150.333C199.543,2158,199.543,2165,199.543,2168.5L199.543,2172"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_21" d="M199.543,2230L199.543,2234.167C199.543,2238.333,199.543,2246.667,199.543,2254.333C199.543,2262,199.543,2269,199.543,2272.5L199.543,2276"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_22" d="M199.543,2334L199.543,2338.167C199.543,2342.333,199.543,2350.667,199.543,2358.333C199.543,2366,199.543,2373,199.543,2376.5L199.543,2380"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_23" d="M199.543,2438L199.543,2442.167C199.543,2446.333,199.543,2454.667,199.543,2462.333C199.543,2470,199.543,2477,199.543,2480.5L199.543,2484"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_24" d="M199.543,2542L199.543,2546.167C199.543,2550.333,199.543,2558.667,199.543,2566.333C199.543,2574,199.543,2581,199.543,2584.5L199.543,2588"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_25" d="M199.543,2646L199.543,2650.167C199.543,2654.333,199.543,2662.667,199.543,2670.333C199.543,2678,199.543,2685,199.543,2688.5L199.543,2692"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_26" d="M199.543,2750L199.543,2754.167C199.543,2758.333,199.543,2766.667,199.543,2774.333C199.543,2782,199.543,2789,199.543,2792.5L199.543,2796"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_27" d="M199.543,2854L199.543,2858.167C199.543,2862.333,199.543,2870.667,199.543,2878.333C199.543,2886,199.543,2893,199.543,2896.5L199.543,2900"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_28" d="M199.543,2958L199.543,2962.167C199.543,2966.333,199.543,2974.667,199.543,2982.333C199.543,2990,199.543,2997,199.543,3000.5L199.543,3004"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_EE_29" d="M145.886,3062L137.605,3066.167C129.325,3070.333,112.764,3078.667,104.484,3086.333C96.203,3094,96.203,3101,96.203,3104.5L96.203,3108"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_FF_30" d="M253.2,3062L261.481,3066.167C269.761,3070.333,286.322,3078.667,294.602,3086.333C302.883,3094,302.883,3101,302.883,3104.5L302.883,3108"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_GG_31" d="M302.883,3166L302.883,3170.167C302.883,3174.333,302.883,3182.667,302.883,3190.333C302.883,3198,302.883,3205,302.883,3208.5L302.883,3212"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GG_HH_32" d="M302.883,3270L302.883,3274.167C302.883,3278.333,302.883,3286.667,302.883,3294.333C302.883,3302,302.883,3309,302.883,3312.5L302.883,3316"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HH_II_33" d="M302.883,3374L302.883,3378.167C302.883,3382.333,302.883,3390.667,302.883,3398.333C302.883,3406,302.883,3413,302.883,3416.5L302.883,3420"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_II_JJ_34" d="M302.883,3478L302.883,3482.167C302.883,3486.333,302.883,3494.667,302.883,3502.333C302.883,3510,302.883,3517,302.883,3520.5L302.883,3524"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JJ_KK_35" d="M302.883,3582L302.883,3586.167C302.883,3590.333,302.883,3598.667,302.883,3606.333C302.883,3614,302.883,3621,302.883,3624.5L302.883,3628"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KK_LL_36" d="M302.883,3686L302.883,3690.167C302.883,3694.333,302.883,3702.667,302.883,3710.333C302.883,3718,302.883,3725,302.883,3728.5L302.883,3732"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LL_MM_37" d="M302.883,3790L302.883,3794.167C302.883,3798.333,302.883,3806.667,302.883,3814.333C302.883,3822,302.883,3829,302.883,3832.5L302.883,3836"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MM_NN_38" d="M302.883,3894L302.883,3898.167C302.883,3902.333,302.883,3910.667,302.883,3918.333C302.883,3926,302.883,3933,302.883,3936.5L302.883,3940"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NN_OO_39" d="M302.883,3998L302.883,4002.167C302.883,4006.333,302.883,4014.667,302.883,4022.333C302.883,4030,302.883,4037,302.883,4040.5L302.883,4044"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OO_PP_40" d="M302.883,4102L302.883,4106.167C302.883,4110.333,302.883,4118.667,302.883,4126.333C302.883,4134,302.883,4141,302.883,4144.5L302.883,4148"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PP_QQ_41" d="M302.883,4206L302.883,4210.167C302.883,4214.333,302.883,4222.667,302.883,4230.333C302.883,4238,302.883,4245,302.883,4248.5L302.883,4252"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QQ_RR_42" d="M302.883,4310L302.883,4314.167C302.883,4318.333,302.883,4326.667,302.953,4334.417C303.023,4342.167,303.164,4349.334,303.234,4352.917L303.304,4356.501"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_SS_43" d="M257.529,4468.693L237.132,4482.419C216.734,4496.144,175.939,4523.596,155.542,4542.821C135.145,4562.047,135.145,4573.047,135.145,4578.547L135.145,4584.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_TT_44" d="M349.236,4468.693L369.467,4482.419C389.698,4496.144,430.16,4523.596,450.39,4542.821C470.621,4562.047,470.621,4573.047,470.621,4578.547L470.621,4584.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SS_UU_45" d="M135.145,4642.047L135.145,4646.214C135.145,4650.38,135.145,4658.714,147.948,4666.849C160.752,4674.985,186.36,4682.924,199.164,4686.893L211.967,4690.862"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TT_UU_46" d="M470.621,4642.047L470.621,4646.214C470.621,4650.38,470.621,4658.714,457.817,4666.849C445.013,4674.985,419.406,4682.924,406.602,4686.893L393.798,4690.862"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UU_VV_47" d="M302.883,4746.047L302.883,4750.214C302.883,4754.38,302.883,4762.714,302.883,4770.38C302.883,4778.047,302.883,4785.047,302.883,4788.547L302.883,4792.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VV_WW_48" d="M302.883,4850.047L302.883,4854.214C302.883,4858.38,302.883,4866.714,302.883,4874.38C302.883,4882.047,302.883,4889.047,302.883,4892.547L302.883,4896.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WW_XX_49" d="M302.883,4954.047L302.883,4958.214C302.883,4962.38,302.883,4970.714,302.883,4978.38C302.883,4986.047,302.883,4993.047,302.883,4996.547L302.883,5000.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_XX_YY_50" d="M302.883,5058.047L302.883,5062.214C302.883,5066.38,302.883,5074.714,302.883,5082.38C302.883,5090.047,302.883,5097.047,302.883,5100.547L302.883,5104.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_YY_ZZ_51" d="M302.883,5162.047L302.883,5166.214C302.883,5170.38,302.883,5178.714,302.883,5186.38C302.883,5194.047,302.883,5201.047,302.883,5204.547L302.883,5208.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ZZ_AAA_52" d="M302.883,5266.047L302.883,5270.214C302.883,5274.38,302.883,5282.714,302.883,5290.38C302.883,5298.047,302.883,5305.047,302.883,5308.547L302.883,5312.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AAA_BBB_53" d="M302.883,5370.047L302.883,5374.214C302.883,5378.38,302.883,5386.714,302.883,5394.38C302.883,5402.047,302.883,5409.047,302.883,5412.547L302.883,5416.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BBB_CCC_54" d="M302.883,5474.047L302.883,5478.214C302.883,5482.38,302.883,5490.714,302.883,5498.38C302.883,5506.047,302.883,5513.047,302.883,5516.547L302.883,5520.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CCC_DDD_55" d="M302.883,5578.047L302.883,5582.214C302.883,5586.38,302.883,5594.714,302.883,5602.38C302.883,5610.047,302.883,5617.047,302.883,5620.547L302.883,5624.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DDD_EEE_56" d="M302.883,5682.047L302.883,5686.214C302.883,5690.38,302.883,5698.714,302.883,5706.38C302.883,5714.047,302.883,5721.047,302.883,5724.547L302.883,5728.047"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EEE_FFF_57" d="M302.883,5786.047L302.883,5790.214C302.883,5794.38,302.883,5802.714,313.534,5816.2C324.185,5829.686,345.488,5848.325,356.139,5857.644L366.791,5866.964"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FFF_R_58" d="M449.584,5883.28L486.247,5871.241C522.909,5859.202,596.234,5835.124,632.896,5814.419C669.559,5793.714,669.559,5776.38,669.559,5759.047C669.559,5741.714,669.559,5724.38,669.559,5707.047C669.559,5689.714,669.559,5672.38,669.559,5655.047C669.559,5637.714,669.559,5620.38,669.559,5603.047C669.559,5585.714,669.559,5568.38,669.559,5551.047C669.559,5533.714,669.559,5516.38,669.559,5499.047C669.559,5481.714,669.559,5464.38,669.559,5447.047C669.559,5429.714,669.559,5412.38,669.559,5395.047C669.559,5377.714,669.559,5360.38,669.559,5343.047C669.559,5325.714,669.559,5308.38,669.559,5291.047C669.559,5273.714,669.559,5256.38,669.559,5239.047C669.559,5221.714,669.559,5204.38,669.559,5187.047C669.559,5169.714,669.559,5152.38,669.559,5135.047C669.559,5117.714,669.559,5100.38,669.559,5083.047C669.559,5065.714,669.559,5048.38,669.559,5031.047C669.559,5013.714,669.559,4996.38,669.559,4979.047C669.559,4961.714,669.559,4944.38,669.559,4927.047C669.559,4909.714,669.559,4892.38,669.559,4875.047C669.559,4857.714,669.559,4840.38,669.559,4823.047C669.559,4805.714,669.559,4788.38,669.559,4771.047C669.559,4753.714,669.559,4736.38,669.559,4719.047C669.559,4701.714,669.559,4684.38,669.559,4667.047C669.559,4649.714,669.559,4632.38,669.559,4613.047C669.559,4593.714,669.559,4572.38,669.559,4542.71C669.559,4513.039,669.559,4475.031,669.559,4439.023C669.559,4403.016,669.559,4369.008,669.559,4343.337C669.559,4317.667,669.559,4300.333,669.559,4283C669.559,4265.667,669.559,4248.333,669.559,4231C669.559,4213.667,669.559,4196.333,669.559,4179C669.559,4161.667,669.559,4144.333,669.559,4127C669.559,4109.667,669.559,4092.333,669.559,4075C669.559,4057.667,669.559,4040.333,669.559,4023C669.559,4005.667,669.559,3988.333,669.559,3971C669.559,3953.667,669.559,3936.333,669.559,3919C669.559,3901.667,669.559,3884.333,669.559,3867C669.559,3849.667,669.559,3832.333,669.559,3815C669.559,3797.667,669.559,3780.333,669.559,3763C669.559,3745.667,669.559,3728.333,669.559,3711C669.559,3693.667,669.559,3676.333,669.559,3659C669.559,3641.667,669.559,3624.333,669.559,3607C669.559,3589.667,669.559,3572.333,669.559,3555C669.559,3537.667,669.559,3520.333,669.559,3503C669.559,3485.667,669.559,3468.333,669.559,3451C669.559,3433.667,669.559,3416.333,669.559,3399C669.559,3381.667,669.559,3364.333,669.559,3347C669.559,3329.667,669.559,3312.333,669.559,3295C669.559,3277.667,669.559,3260.333,669.559,3243C669.559,3225.667,669.559,3208.333,669.559,3191C669.559,3173.667,669.559,3156.333,669.559,3139C669.559,3121.667,669.559,3104.333,669.559,3087C669.559,3069.667,669.559,3052.333,669.559,3035C669.559,3017.667,669.559,3000.333,669.559,2983C669.559,2965.667,669.559,2948.333,669.559,2931C669.559,2913.667,669.559,2896.333,669.559,2879C669.559,2861.667,669.559,2844.333,669.559,2827C669.559,2809.667,669.559,2792.333,669.559,2775C669.559,2757.667,669.559,2740.333,669.559,2723C669.559,2705.667,669.559,2688.333,669.559,2671C669.559,2653.667,669.559,2636.333,669.559,2619C669.559,2601.667,669.559,2584.333,669.559,2567C669.559,2549.667,669.559,2532.333,669.559,2515C669.559,2497.667,669.559,2480.333,669.559,2463C669.559,2445.667,669.559,2428.333,669.559,2411C669.559,2393.667,669.559,2376.333,669.559,2359C669.559,2341.667,669.559,2324.333,669.559,2307C669.559,2289.667,669.559,2272.333,669.559,2255C669.559,2237.667,669.559,2220.333,669.559,2203C669.559,2185.667,669.559,2168.333,669.559,2151C669.559,2133.667,669.559,2116.333,669.559,2099C669.559,2081.667,669.559,2064.333,669.559,2047C669.559,2029.667,669.559,2012.333,669.559,1995C669.559,1977.667,669.559,1960.333,669.559,1943C669.559,1925.667,669.559,1908.333,669.559,1891C669.559,1873.667,669.559,1856.333,624.796,1841.385C580.033,1826.437,490.507,1813.873,445.744,1807.591L400.981,1801.31"></path><path marker-end="url(#mermaid-b4139eb8-ba49-4645-a419-6acdeaebdaf3_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FFF_GGG_59" d="M402.852,5960.422L402.768,5966.505C402.685,5972.589,402.518,5984.755,402.435,5996.339C402.352,6007.922,402.352,6018.922,402.352,6024.422L402.352,6029.922"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(93.01171875, 1515)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>失败</p></span></div></foreignObject></g></g><g transform="translate(299.01171875, 1515)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>成功</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(135.14453125, 4551.046875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(470.62109375, 4551.046875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(669.55859375, 3763)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(402.3515625, 5996.921875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(196.01171875, 35)" id="flowchart-A-726" class="node default"><rect height="54" width="118.78125" y="-27" x="-59.390625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-29.390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="58.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM.<strong>init</strong></p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 139)" id="flowchart-B-727" class="node default"><rect height="54" width="169.546875" y="-27" x="-84.7734375" style="" class="basic label-container"></rect><g transform="translate(-54.7734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EngineArgs创建</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 243)" id="flowchart-C-729" class="node default"><rect height="54" width="268.25" y="-27" x="-134.125" style="" class="basic label-container"></rect><g transform="translate(-104.125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="208.25"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLMEngine.from_engine_args</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 347)" id="flowchart-D-731" class="node default"><rect height="54" width="157.796875" y="-27" x="-78.8984375" style="" class="basic label-container"></rect><g transform="translate(-48.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>V1引擎初始化</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 451)" id="flowchart-E-733" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模型配置解析</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 555)" id="flowchart-F-735" class="node default"><rect height="54" width="245.25" y="-27" x="-122.625" style="" class="basic label-container"></rect><g transform="translate(-92.625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检测Qwen2+SmoothQuant</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 659)" id="flowchart-G-737" class="node default"><rect height="54" width="163.109375" y="-27" x="-81.5546875" style="" class="basic label-container"></rect><g transform="translate(-51.5546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="103.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建Fp8Config</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 763)" id="flowchart-H-739" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模型加载</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 867)" id="flowchart-I-741" class="node default"><rect height="54" width="204.578125" y="-27" x="-102.2890625" style="" class="basic label-container"></rect><g transform="translate(-72.2890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qwen2模型结构创建</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 971)" id="flowchart-J-743" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>量化线性层替换</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 1075)" id="flowchart-K-745" class="node default"><rect height="54" width="231.125" y="-27" x="-115.5625" style="" class="basic label-container"></rect><g transform="translate(-85.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="171.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Fp8LinearMethod初始化</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 1179)" id="flowchart-L-747" class="node default"><rect height="54" width="236" y="-27" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建量化权重和缩放因子</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 1283)" id="flowchart-M-749" class="node default"><rect height="54" width="158.609375" y="-27" x="-79.3046875" style="" class="basic label-container"></rect><g transform="translate(-49.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>KV缓存初始化</p></span></div></foreignObject></g></g><g transform="translate(196.01171875, 1419)" id="flowchart-N-751" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内存检查</p></span></div></foreignObject></g></g><g transform="translate(93.01171875, 1579)" id="flowchart-O-753" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内存不足错误</p></span></div></foreignObject></g></g><g transform="translate(299.01171875, 1579)" id="flowchart-P-755" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模型加载完成</p></span></div></foreignObject></g></g><g transform="translate(299.01171875, 1683)" id="flowchart-Q-757" class="node default"><rect height="54" width="184.515625" y="-27" x="-92.2578125" style="" class="basic label-container"></rect><g transform="translate(-62.2578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>llm.generate调用</p></span></div></foreignObject></g></g><g transform="translate(299.01171875, 1787)" id="flowchart-R-759" class="node default"><rect height="54" width="196.015625" y="-27" x="-98.0078125" style="" class="basic label-container"></rect><g transform="translate(-68.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>_run_engine主循环</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 1891)" id="flowchart-S-761" class="node default"><rect height="54" width="174.984375" y="-27" x="-87.4921875" style="" class="basic label-container"></rect><g transform="translate(-57.4921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>llm_engine.step</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 1995)" id="flowchart-T-763" class="node default"><rect height="54" width="176.8125" y="-27" x="-88.40625" style="" class="basic label-container"></rect><g transform="translate(-58.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EngineCore.step</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2099)" id="flowchart-U-765" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调度器选择批次</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2203)" id="flowchart-V-767" class="node default"><rect height="54" width="169.828125" y="-27" x="-84.9140625" style="" class="basic label-container"></rect><g transform="translate(-54.9140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>execute_model</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2307)" id="flowchart-W-769" class="node default"><rect height="54" width="257.375" y="-27" x="-128.6875" style="" class="basic label-container"></rect><g transform="translate(-98.6875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="197.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>gpu_worker.execute_model</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2411)" id="flowchart-X-771" class="node default"><rect height="54" width="307.65625" y="-27" x="-153.828125" style="" class="basic label-container"></rect><g transform="translate(-123.828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="247.65625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>gpu_model_runner.execute_model</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2515)" id="flowchart-Y-773" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>准备输入数据</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2619)" id="flowchart-Z-775" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置前向传播上下文</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2723)" id="flowchart-AA-777" class="node default"><rect height="54" width="226.734375" y="-27" x="-113.3671875" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-83.3671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="166.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔥 self.model前向传播</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2827)" id="flowchart-BB-779" class="node default"><rect height="54" width="259.59375" y="-27" x="-129.796875" style="" class="basic label-container"></rect><g transform="translate(-99.796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="199.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qwen2ForCausalLM.forward</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 2931)" id="flowchart-CC-781" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>词嵌入层</p></span></div></foreignObject></g></g><g transform="translate(199.54296875, 3035)" id="flowchart-DD-783" class="node default"><rect height="54" width="194.328125" y="-27" x="-97.1640625" style="" class="basic label-container"></rect><g transform="translate(-67.1640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="134.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Transformer层循环</p></span></div></foreignObject></g></g><g transform="translate(96.203125, 3139)" id="flowchart-EE-785" class="node default"><rect height="54" width="176.40625" y="-27" x="-88.203125" style="" class="basic label-container"></rect><g transform="translate(-58.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qwen2Attention</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3139)" id="flowchart-FF-787" class="node default"><rect height="54" width="136.953125" y="-27" x="-68.4765625" style="" class="basic label-container"></rect><g transform="translate(-38.4765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="76.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qwen2MLP</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3243)" id="flowchart-GG-789" class="node default"><rect height="54" width="235.53125" y="-27" x="-117.765625" style="" class="basic label-container"></rect><g transform="translate(-87.765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="175.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>gate_up_proj量化线性层</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3347)" id="flowchart-HH-791" class="node default"><rect height="54" width="199.46875" y="-27" x="-99.734375" style="" class="basic label-container"></rect><g transform="translate(-69.734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="139.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LinearBase.forward</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3451)" id="flowchart-II-793" class="node default"><rect height="54" width="208.859375" y="-27" x="-104.4296875" style="" class="basic label-container"></rect><g transform="translate(-74.4296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="148.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>quant_method.apply</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3555)" id="flowchart-JJ-795" class="node default"><rect height="54" width="227.84375" y="-27" x="-113.921875" style="" class="basic label-container"></rect><g transform="translate(-83.921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Fp8LinearMethod.apply</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3659)" id="flowchart-KK-797" class="node default"><rect height="54" width="175.578125" y="-27" x="-87.7890625" style="" class="basic label-container"></rect><g transform="translate(-57.7890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>fp8_linear.apply</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3763)" id="flowchart-LL-799" class="node default"><rect height="54" width="226.875" y="-27" x="-113.4375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-83.4375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="166.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Fp8LinearOp.apply核心</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3867)" id="flowchart-MM-801" class="node default"><rect height="54" width="158.203125" y="-27" x="-79.1015625" style="" class="basic label-container"></rect><g transform="translate(-49.1015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>输入重塑为2D</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 3971)" id="flowchart-NN-803" class="node default"><rect height="54" width="182.796875" y="-27" x="-91.3984375" style="fill:#ffeb3b !important" class="basic label-container"></rect><g transform="translate(-61.3984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔥 动态激活量化</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4075)" id="flowchart-OO-805" class="node default"><rect height="54" width="216.171875" y="-27" x="-108.0859375" style="" class="basic label-container"></rect><g transform="translate(-78.0859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="156.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ops.scaled_fp8_quant</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4179)" id="flowchart-PP-807" class="node default"><rect height="54" width="217.46875" y="-27" x="-108.734375" style="" class="basic label-container"></rect><g transform="translate(-78.734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="157.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FP16→INT8 per-token</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4283)" id="flowchart-QQ-809" class="node default"><rect height="54" width="198.09375" y="-27" x="-99.046875" style="" class="basic label-container"></rect><g transform="translate(-69.046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="138.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>选择量化GEMM实现</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4437.0234375)" id="flowchart-RR-811" class="node default"><polygon transform="translate(-77.0234375,77.0234375)" class="label-container" points="77.0234375,0 154.046875,-77.0234375 77.0234375,-154.046875 0,-77.0234375"></polygon><g transform="translate(-50.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CUTLASS支持?</p></span></div></foreignObject></g></g><g transform="translate(135.14453125, 4615.046875)" id="flowchart-SS-813" class="node default"><rect height="54" width="243.078125" y="-27" x="-121.5390625" style="" class="basic label-container"></rect><g transform="translate(-91.5390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="183.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>cutlass_w8a8_scaled_mm</p></span></div></foreignObject></g></g><g transform="translate(470.62109375, 4615.046875)" id="flowchart-TT-815" class="node default"><rect height="54" width="327.875" y="-27" x="-163.9375" style="" class="basic label-container"></rect><g transform="translate(-133.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="267.875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>torch_channelwise_w8a8_scaled_mm</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4719.046875)" id="flowchart-UU-817" class="node default"><rect height="54" width="256.890625" y="-27" x="-128.4453125" style="fill:#4caf50 !important" class="basic label-container"></rect><g transform="translate(-98.4453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="196.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔥 INT8×INT8→FP16 GEMM</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4823.046875)" id="flowchart-VV-821" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用缩放因子反量化</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 4927.046875)" id="flowchart-WW-823" class="node default"><rect height="54" width="158.109375" y="-27" x="-79.0546875" style="" class="basic label-container"></rect><g transform="translate(-49.0546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回FP16结果</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5031.046875)" id="flowchart-XX-825" class="node default"><rect height="54" width="216.125" y="-27" x="-108.0625" style="" class="basic label-container"></rect><g transform="translate(-78.0625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="156.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>down_proj量化线性层</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5135.046875)" id="flowchart-YY-827" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>重复量化计算流程</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5239.046875)" id="flowchart-ZZ-829" class="node default"><rect height="54" width="120.375" y="-27" x="-60.1875" style="" class="basic label-container"></rect><g transform="translate(-30.1875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="60.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MLP输出</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5343.046875)" id="flowchart-AAA-831" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一层</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5447.046875)" id="flowchart-BBB-833" class="node default"><rect height="54" width="201.1875" y="-27" x="-100.59375" style="" class="basic label-container"></rect><g transform="translate(-70.59375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="141.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>最终输出层lm_head</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5551.046875)" id="flowchart-CCC-835" class="node default"><rect height="54" width="134.109375" y="-27" x="-67.0546875" style="" class="basic label-container"></rect><g transform="translate(-37.0546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="74.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logits计算</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5655.046875)" id="flowchart-DDD-837" class="node default"><rect height="54" width="164.46875" y="-27" x="-82.234375" style="" class="basic label-container"></rect><g transform="translate(-52.234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>采样生成token</p></span></div></foreignObject></g></g><g transform="translate(302.8828125, 5759.046875)" id="flowchart-EEE-839" class="node default"><rect height="54" width="142.609375" y="-27" x="-71.3046875" style="" class="basic label-container"></rect><g transform="translate(-41.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="82.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新KV缓存</p></span></div></foreignObject></g></g><g transform="translate(402.3515625, 5897.984375)" id="flowchart-FFF-841" class="node default"><polygon transform="translate(-61.9375,61.9375)" class="label-container" points="61.9375,0 123.875,-61.9375 61.9375,-123.875 0,-61.9375"></polygon><g transform="translate(-34.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>序列完成?</p></span></div></foreignObject></g></g><g transform="translate(402.3515625, 6060.921875)" id="flowchart-GGG-845" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回生成结果</p></span></div></foreignObject></g></g></g></g></g></svg>