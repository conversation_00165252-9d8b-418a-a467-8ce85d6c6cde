2025-08-06 08:49:18.141 | DEBUG    | llmcompressor.core.lifecycle:reset:61 - Resetting compression lifecycle
2025-08-06 08:49:18.141 | INFO     | llmcompressor.core.lifecycle:reset:73 - Compression lifecycle reset
2025-08-06 08:49:18.141 | DEBUG    | llmcompressor.core.state:update:182 - Updating state with provided parameters: {'model': LlamaForCausalLM(
  (model): LlamaModel(
    (embed_tokens): Embedding(32001, 16)
    (layers): ModuleList(
      (0): LlamaDecoderLayer(
        (self_attn): LlamaAttention(
          (q_proj): Linear(in_features=16, out_features=1024, bias=False)
          (k_proj): Linear(in_features=16, out_features=1024, bias=False)
          (v_proj): Linear(in_features=16, out_features=1024, bias=False)
          (o_proj): Linear(in_features=1024, out_features=16, bias=False)
        )
        (mlp): LlamaMLP(
          (gate_proj): Linear(in_features=16, out_features=32, bias=False)
          (up_proj): Linear(in_features=16, out_features=32, bias=False)
          (down_proj): Linear(in_features=32, out_features=16, bias=False)
          (act_fn): SiLU()
        )
        (input_layernorm): LlamaRMSNorm((16,), eps=1e-05)
        (post_attention_layernorm): LlamaRMSNorm((16,), eps=1e-05)
      )
    )
    (norm): LlamaRMSNorm((16,), eps=1e-05)
    (rotary_emb): LlamaRotaryEmbedding()
  )
  (lm_head): Linear(in_features=16, out_features=32001, bias=False)
), 'teacher_model': None, 'optimizer': None, 'attach_optim_callbacks': True, 'train_data': None, 'val_data': None, 'test_data': None, 'calib_data': <torch.utils.data.dataloader.DataLoader object at 0x7f8a3e3d48b0>, 'copy_data': True, 'start': -1, 'steps_per_epoch': None, 'batches_per_step': None, 'loggers': None, 'model_log_cadence': None, 'kwargs': {}}
2025-08-06 08:49:18.142 | DEBUG    | llmcompressor.core.lifecycle:initialize:94 - Initializing compression lifecycle
2025-08-06 08:49:18.142 | INFO     | llmcompressor.recipe.recipe:from_modifiers:59 - Creating recipe from modifiers
2025-08-06 08:49:18.175 | INFO     | llmcompressor.modifiers.smoothquant.base:_infer_mappings_from_model:183 - No SmoothQuantModifier.mappings provided, inferring from model...
2025-08-06 08:49:18.179 | DEBUG    | llmcompressor.core.lifecycle:initialize:103 - Initialized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=False
2025-08-06 08:49:18.179 | INFO     | llmcompressor.core.lifecycle:initialize:108 - Compression lifecycle initialized for 1 modifiers
2025-08-06 08:49:18.179 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `SmoothQuantModifier`
2025-08-06 08:49:18.955 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.956 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-06 08:49:18.956 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.961 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-06 08:49:18.962 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:49:18.963 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:49:18.964 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:19.008 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-06 08:49:19.008 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7f8a4f73ae00>
2025-08-06 08:49:19.009 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - index=0 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False smoothing_strength=0.8 mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')] ignore=[] num_calibration_steps=None calibration_function=None added <torch.utils.hooks.RemovableHandle object at 0x7f8a4f738f10>
2025-08-06 08:49:19.009 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-06 08:49:22.680 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-06 08:49:22.680 | INFO     | llmcompressor.modifiers.smoothquant.base:_apply_smoothing:271 - Smoothing with model.layers.0.input_layernorm
2025-08-06 08:49:22.698 | INFO     | llmcompressor.modifiers.smoothquant.base:_apply_smoothing:271 - Smoothing with model.layers.0.post_attention_layernorm
2025-08-06 08:49:22.698 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-06 08:49:24.997 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-06 08:49:24.997 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-06 08:49:25.137 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-06 08:49:25.138 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None)] index=0 group='SmoothQuantModifier' applied=False
2025-08-06 08:49:25.138 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `GPTQModifier`
2025-08-06 08:49:25.151 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.151 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-06 08:49:25.152 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.156 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.156 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-06 08:49:25.156 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-06 08:49:25.157 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:189 - ---- Autowrapper ----
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:190 - self._update_causal_mask(attention_mask, inputs_embeds, cache_position, past_key_values, output_attentions)
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:191 - ---------------------
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-06 08:49:25.158 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.159 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:49:25.159 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:49:25.159 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:49:25.179 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_START
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a4f7c6ad0>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a4f7c6020>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a29ff0550>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a29ff1cc0>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a29ff1b70>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a29ff1ab0>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.modifiers.utils.hooks:register_hook:98 - config_groups=None targets=['Linear'] ignore=['lm_head'] scheme='W8A8' kv_cache_scheme=None index=1 group='default' start=None end=None update=None initialized_=True finalized_=False started_=True ended_=False sequential_update=True sequential_targets=None block_size=128 dampening_frac=0.01 actorder=None offload_hessians=False added <torch.utils.hooks.RemovableHandle object at 0x7f8a29ff1c30>
2025-08-06 08:49:25.180 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-06 08:49:28.664 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-06 08:49:28.664 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.q_proj using 512 samples
2025-08-06 08:49:28.739 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.07s
2025-08-06 08:49:28.739 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-06 08:49:28.891 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 4.74% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:28.892 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-06 08:49:28.892 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.k_proj using 512 samples
2025-08-06 08:49:28.901 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-06 08:49:28.901 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-06 08:49:30.295 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:30.295 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.296 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.296 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.296 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.296 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 4.74% | total memory: 42 GB
2025-08-06 08:49:30.296 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.297 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.297 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-06 08:49:30.298 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.v_proj using 512 samples
2025-08-06 08:49:30.322 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.02s
2025-08-06 08:49:30.323 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.01
2025-08-06 08:49:30.532 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:30.533 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.533 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.533 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.533 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.534 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 4.74% | total memory: 42 GB
2025-08-06 08:49:30.534 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.534 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.534 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.03584 MB
2025-08-06 08:49:30.535 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.self_attn.o_proj using 512 samples
2025-08-06 08:49:30.914 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.38s
2025-08-06 08:49:30.914 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 5.80% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.919 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.032816 MB
2025-08-06 08:49:30.920 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.gate_proj using 512 samples
2025-08-06 08:49:30.929 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-06 08:49:30.930 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 5.80% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.935 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-06 08:49:30.936 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.up_proj using 512 samples
2025-08-06 08:49:30.947 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.01s
2025-08-06 08:49:30.947 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.53% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 5.80% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.950 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.00112 MB
2025-08-06 08:49:30.951 | INFO     | llmcompressor.modifiers.quantization.gptq.base:compress_modules:260 - Quantizing model.layers.0.mlp.down_proj using 512 samples
2025-08-06 08:49:30.973 | METRIC   | llmcompressor.utils.metric_logging:compress:127 - time 0.02s
2025-08-06 08:49:30.973 | METRIC   | llmcompressor.utils.metric_logging:compress:129 - error 0.00
2025-08-06 08:49:30.974 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 0 | usage: 6.54% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 1 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 2 | usage: 6.28% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 3 | usage: 2.65% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 4 | usage: 85.60% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 5 | usage: 5.81% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 6 | usage: 16.65% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:136 - GPU 7 | usage: 3.33% | total memory: 42 GB
2025-08-06 08:49:30.975 | METRIC   | llmcompressor.utils.metric_logging:compress:145 - Compressed module size: 0.001072 MB
2025-08-06 08:49:30.975 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-06 08:49:33.730 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.SEQUENTIAL_EPOCH_END
2025-08-06 08:49:33.730 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-06 08:49:33.887 | DEBUG    | llmcompressor.core.lifecycle:event:191 - Handling event: EventType.CALIBRATION_EPOCH_END
2025-08-06 08:49:33.887 | DEBUG    | llmcompressor.core.lifecycle:event:201 - Updated event with modifier: modifiers=[GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=1 group='GPTQModifier' applied=False
2025-08-06 08:49:33.887 | DEBUG    | llmcompressor.core.lifecycle:finalize:131 - Finalizing compression lifecycle
2025-08-06 08:49:33.888 | DEBUG    | llmcompressor.core.lifecycle:finalize:135 - Finalized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=True, started_=True, ended_=True, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=True
2025-08-06 08:49:33.888 | INFO     | llmcompressor.core.lifecycle:finalize:141 - Compression lifecycle finalized for 1 modifiers
2025-08-06 08:49:33.892 | WARNING  | llmcompressor.entrypoints.utils:post_process:107 - Optimized model is not saved. To save, please provide`output_dir` as input arg.Ex. `oneshot(..., output_dir=...)`
2025-08-06 08:49:33.892 | INFO     | llmcompressor.transformers.sparsification.compressed_tensors_utils:get_model_compressor:217 - skip_sparsity_compression_stats set to True. Skipping sparsity compression statistic calculations. No sparsity compressor will be applied.
2025-08-06 08:49:33.934 | DEBUG    | llmcompressor.transformers.utils.helpers:infer_recipe_from_model_path:105 - No recipe found in the model_path: /home/<USER>/single_llama
