2025-08-06 08:37:39.004 | DEBUG    | llmcompressor.core.lifecycle:reset:61 - Resetting compression lifecycle
2025-08-06 08:37:39.005 | INFO     | llmcompressor.core.lifecycle:reset:73 - Compression lifecycle reset
2025-08-06 08:37:39.005 | DEBUG    | llmcompressor.core.state:update:182 - Updating state with provided parameters: {'model': LlamaForCausalLM(
  (model): LlamaModel(
    (embed_tokens): Embedding(32, 16)
    (layers): ModuleList(
      (0): LlamaDecoderLayer(
        (self_attn): LlamaAttention(
          (q_proj): Linear(in_features=16, out_features=1024, bias=False)
          (k_proj): Linear(in_features=16, out_features=1024, bias=False)
          (v_proj): Linear(in_features=16, out_features=1024, bias=False)
          (o_proj): Linear(in_features=1024, out_features=16, bias=False)
        )
        (mlp): LlamaMLP(
          (gate_proj): Linear(in_features=16, out_features=32, bias=False)
          (up_proj): Linear(in_features=16, out_features=32, bias=False)
          (down_proj): Linear(in_features=32, out_features=16, bias=False)
          (act_fn): SiLU()
        )
        (input_layernorm): LlamaRMSNorm((16,), eps=1e-05)
        (post_attention_layernorm): LlamaRMSNorm((16,), eps=1e-05)
      )
    )
    (norm): LlamaRMSNorm((16,), eps=1e-05)
    (rotary_emb): LlamaRotaryEmbedding()
  )
  (lm_head): Linear(in_features=16, out_features=32, bias=False)
), 'teacher_model': None, 'optimizer': None, 'attach_optim_callbacks': True, 'train_data': None, 'val_data': None, 'test_data': None, 'calib_data': <torch.utils.data.dataloader.DataLoader object at 0x769e0754ccd0>, 'copy_data': True, 'start': -1, 'steps_per_epoch': None, 'batches_per_step': None, 'loggers': None, 'model_log_cadence': None, 'kwargs': {}}
2025-08-06 08:37:39.006 | DEBUG    | llmcompressor.core.lifecycle:initialize:94 - Initializing compression lifecycle
2025-08-06 08:37:39.006 | INFO     | llmcompressor.recipe.recipe:from_modifiers:59 - Creating recipe from modifiers
2025-08-06 08:37:39.043 | INFO     | llmcompressor.modifiers.smoothquant.base:_infer_mappings_from_model:183 - No SmoothQuantModifier.mappings provided, inferring from model...
2025-08-06 08:37:39.057 | DEBUG    | llmcompressor.core.lifecycle:initialize:103 - Initialized modifier: modifiers=[SmoothQuantModifier(index=0, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, smoothing_strength=0.8, mappings=[LayerMap(balance_layers=['re:.*q_proj', 're:.*k_proj', 're:.*v_proj'], smooth_layers='re:.*input_layernorm'), LayerMap(balance_layers=['re:.*gate_proj', 're:.*up_proj'], smooth_layers='re:.*post_attention_layernorm')], ignore=[], num_calibration_steps=None, calibration_function=None), GPTQModifier(config_groups=None, targets=['Linear'], ignore=['lm_head'], scheme='W8A8', kv_cache_scheme=None, index=1, group='default', start=None, end=None, update=None, initialized_=True, finalized_=False, started_=False, ended_=False, sequential_update=True, sequential_targets=None, block_size=128, dampening_frac=0.01, actorder=None, offload_hessians=False)] index=0 group='default' applied=False
2025-08-06 08:37:39.057 | INFO     | llmcompressor.core.lifecycle:initialize:108 - Compression lifecycle initialized for 1 modifiers
2025-08-06 08:37:39.058 | INFO     | llmcompressor.pipelines.independent.pipeline:IndependentPipeline:47 - Inferred `SequentialPipeline` for `SmoothQuantModifier`
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if (input_ids is None) ^ (inputs_embeds is not None):
    raise ValueError('You must specify exactly one of input_ids or inputs_embeds')
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if not isinstance(past_key_values, (type(None), Cache)):
    raise ValueError('The `past_key_values` should be either a `Cache` object or `None`.')
2025-08-06 08:37:39.071 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if inputs_embeds is None:
    inputs_embeds = self.embed_tokens(input_ids)
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if use_cache and past_key_values is None:
    past_key_values = DynamicCache()
2025-08-06 08:37:39.072 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.073 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if cache_position is None:
    past_seen_tokens = past_key_values.get_seq_length() if past_key_values is not None else 0
    cache_position = torch.arange(past_seen_tokens, past_seen_tokens + inputs_embeds.shape[1], device=inputs_embeds.device)
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if position_ids is None:
    position_ids = cache_position.unsqueeze(0)
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_attentions:
    all_self_attns += (layer_outputs[1],)
2025-08-06 08:37:39.074 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.075 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.075 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if output_hidden_states:
    all_hidden_states += (hidden_states,)
2025-08-06 08:37:39.075 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
2025-08-06 08:37:39.078 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:183 - ---- Autowrapper ----
2025-08-06 08:37:39.079 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:184 - if labels is not None:
    loss = self.loss_function(logits=logits, labels=labels, vocab_size=self.config.vocab_size, **kwargs)
2025-08-06 08:37:39.079 | DEBUG    | llmcompressor.pipelines.sequential.ast_utils.auto_wrapper:_wrap_if_possible:185 - ---------------------
