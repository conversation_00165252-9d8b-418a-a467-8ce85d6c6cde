<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1249.2421875 3211.875" style="max-width: 1249.2421875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf"><style>#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .error-icon{fill:#a44141;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .error-text{fill:#ddd;stroke:#ddd;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-thickness-normal{stroke-width:1px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-thickness-thick{stroke-width:3.5px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-pattern-solid{stroke-dasharray:0;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .marker.cross{stroke:lightgrey;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf p{margin:0;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster-label text{fill:#F9FFFE;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster-label span{color:#F9FFFE;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster-label span p{background-color:transparent;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .label text,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf span{fill:#ccc;color:#ccc;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node rect,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node circle,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node ellipse,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node polygon,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .rough-node .label text,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node .label text,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .image-shape .label,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .icon-shape .label{text-anchor:middle;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .rough-node .label,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node .label,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .image-shape .label,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .icon-shape .label{text-align:center;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .node.clickable{cursor:pointer;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .arrowheadPath{fill:lightgrey;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster text{fill:#F9FFFE;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .cluster span{color:#F9FFFE;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf rect.text{fill:none;stroke-width:0;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .icon-shape,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .icon-shape p,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .icon-shape rect,#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M579.227,62L579.227,66.167C579.227,70.333,579.227,78.667,579.227,86.333C579.227,94,579.227,101,579.227,104.5L579.227,108"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M579.227,166L579.227,170.167C579.227,174.333,579.227,182.667,579.227,190.333C579.227,198,579.227,205,579.227,208.5L579.227,212"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M579.227,270L579.227,274.167C579.227,278.333,579.227,286.667,579.227,294.333C579.227,302,579.227,309,579.227,312.5L579.227,316"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M579.227,374L579.227,378.167C579.227,382.333,579.227,390.667,579.227,398.333C579.227,406,579.227,413,579.227,416.5L579.227,420"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M579.227,478L579.227,482.167C579.227,486.333,579.227,494.667,579.227,502.333C579.227,510,579.227,517,579.227,520.5L579.227,524"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M579.227,582L579.227,586.167C579.227,590.333,579.227,598.667,579.227,606.333C579.227,614,579.227,621,579.227,624.5L579.227,628"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M579.227,686L579.227,690.167C579.227,694.333,579.227,702.667,579.227,710.333C579.227,718,579.227,725,579.227,728.5L579.227,732"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M579.227,790L579.227,794.167C579.227,798.333,579.227,806.667,579.227,814.333C579.227,822,579.227,829,579.227,832.5L579.227,836"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M579.227,894L579.227,898.167C579.227,902.333,579.227,910.667,579.297,918.417C579.367,926.167,579.508,933.334,579.578,936.917L579.648,940.501"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M623.09,1019.137L660.15,1032.447C697.211,1045.758,771.332,1072.379,808.393,1091.189C845.453,1110,845.453,1121,845.453,1126.5L845.453,1132"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_L_10" d="M532.702,1015.475L477.625,1029.396C422.548,1043.317,312.395,1071.158,257.319,1090.579C202.242,1110,202.242,1121,202.242,1126.5L202.242,1132"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M202.242,1190L202.242,1194.167C202.242,1198.333,202.242,1206.667,202.242,1214.333C202.242,1222,202.242,1229,202.242,1232.5L202.242,1236"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M202.242,1294L202.242,1298.167C202.242,1302.333,202.242,1310.667,202.242,1320.333C202.242,1330,202.242,1341,202.242,1346.5L202.242,1352"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M202.242,1410L202.242,1416.167C202.242,1422.333,202.242,1434.667,202.242,1444.333C202.242,1454,202.242,1461,202.242,1464.5L202.242,1468"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M202.242,1526L202.242,1530.167C202.242,1534.333,202.242,1542.667,202.242,1550.333C202.242,1558,202.242,1565,202.242,1568.5L202.242,1572"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M202.242,1630L202.242,1634.167C202.242,1638.333,202.242,1646.667,202.242,1654.333C202.242,1662,202.242,1669,202.242,1672.5L202.242,1676"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M202.242,1734L202.242,1738.167C202.242,1742.333,202.242,1750.667,202.242,1758.333C202.242,1766,202.242,1773,202.242,1776.5L202.242,1780"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M162.566,1838L156.443,1842.167C150.32,1846.333,138.074,1854.667,131.951,1862.333C125.828,1870,125.828,1877,125.828,1880.5L125.828,1884"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_18" d="M125.828,1942L125.828,1946.167C125.828,1950.333,125.828,1958.667,125.828,1966.333C125.828,1974,125.828,1981,125.828,1984.5L125.828,1988"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_19" d="M125.828,2046L125.828,2050.167C125.828,2054.333,125.828,2062.667,125.828,2070.333C125.828,2078,125.828,2085,125.828,2088.5L125.828,2092"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_20" d="M125.828,2150L125.828,2154.167C125.828,2158.333,125.828,2166.667,125.828,2174.333C125.828,2182,125.828,2189,125.828,2192.5L125.828,2196"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_21" d="M125.828,2254L125.828,2258.167C125.828,2262.333,125.828,2270.667,125.828,2278.333C125.828,2286,125.828,2293,125.828,2296.5L125.828,2300"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_22" d="M125.828,2358L125.828,2364.167C125.828,2370.333,125.828,2382.667,125.828,2394.333C125.828,2406,125.828,2417,125.828,2422.5L125.828,2428"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_23" d="M125.828,2486L125.828,2490.167C125.828,2494.333,125.828,2502.667,125.828,2510.333C125.828,2518,125.828,2525,125.828,2528.5L125.828,2532"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_24" d="M125.828,2590L125.828,2594.167C125.828,2598.333,125.828,2606.667,125.828,2614.333C125.828,2622,125.828,2629,125.828,2632.5L125.828,2636"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_25" d="M125.828,2694L125.828,2698.167C125.828,2702.333,125.828,2710.667,125.828,2718.333C125.828,2726,125.828,2733,125.828,2736.5L125.828,2740"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_26" d="M125.828,2798L125.828,2802.167C125.828,2806.333,125.828,2814.667,125.828,2822.333C125.828,2830,125.828,2837,125.828,2840.5L125.828,2844"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_27" d="M125.828,2902L125.828,2906.167C125.828,2910.333,125.828,2918.667,133.378,2931.412C140.927,2944.157,156.027,2961.314,163.576,2969.892L171.126,2978.471"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_R_28" d="M231.716,2981.474L239.539,2972.395C247.363,2963.316,263.009,2945.158,270.833,2927.412C278.656,2909.667,278.656,2892.333,278.656,2875C278.656,2857.667,278.656,2840.333,278.656,2823C278.656,2805.667,278.656,2788.333,278.656,2771C278.656,2753.667,278.656,2736.333,278.656,2719C278.656,2701.667,278.656,2684.333,278.656,2667C278.656,2649.667,278.656,2632.333,278.656,2615C278.656,2597.667,278.656,2580.333,278.656,2563C278.656,2545.667,278.656,2528.333,278.656,2511C278.656,2493.667,278.656,2476.333,278.656,2457C278.656,2437.667,278.656,2416.333,278.656,2395C278.656,2373.667,278.656,2352.333,278.656,2333C278.656,2313.667,278.656,2296.333,278.656,2279C278.656,2261.667,278.656,2244.333,278.656,2227C278.656,2209.667,278.656,2192.333,278.656,2175C278.656,2157.667,278.656,2140.333,278.656,2123C278.656,2105.667,278.656,2088.333,278.656,2071C278.656,2053.667,278.656,2036.333,278.656,2019C278.656,2001.667,278.656,1984.333,278.656,1967C278.656,1949.667,278.656,1932.333,278.656,1915C278.656,1897.667,278.656,1880.333,273.084,1867.875C267.513,1855.417,256.369,1847.834,250.797,1844.042L245.226,1840.25"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_29" d="M202.742,3076.375L202.659,3082.458C202.576,3088.542,202.409,3100.708,202.326,3112.292C202.242,3123.875,202.242,3134.875,202.242,3140.375L202.242,3145.875"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_EE_30" d="M845.453,1190L845.453,1194.167C845.453,1198.333,845.453,1206.667,845.453,1214.333C845.453,1222,845.453,1229,845.453,1232.5L845.453,1236"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_FF_31" d="M783.453,1275.199L728.251,1282.499C673.049,1289.799,562.646,1304.4,507.444,1315.2C452.242,1326,452.242,1333,452.242,1336.5L452.242,1340"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_GG_32" d="M787.944,1294L779.069,1298.167C770.195,1302.333,752.445,1310.667,743.57,1320.333C734.695,1330,734.695,1341,734.695,1346.5L734.695,1352"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_HH_33" d="M902.962,1294L911.837,1298.167C920.712,1302.333,938.461,1310.667,947.336,1320.333C956.211,1330,956.211,1341,956.211,1346.5L956.211,1352"></path><path marker-end="url(#mermaid-59d03dcd-b218-4d67-a7a9-12f6352756cf_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_II_34" d="M907.453,1277.307L949.254,1284.256C991.055,1291.205,1074.656,1305.102,1116.457,1317.551C1158.258,1330,1158.258,1341,1158.258,1346.5L1158.258,1352"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(845.453125, 1099)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>失败</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1099)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>成功</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(278.65625, 2395)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 3112.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(579.2265625, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="135.5625" y="-27" x="-67.78125" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-37.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="75.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM初始化</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 139)" id="flowchart-B-1" class="node default"><rect height="54" width="169.546875" y="-27" x="-84.7734375" style="" class="basic label-container"></rect><g transform="translate(-54.7734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建EngineArgs</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 243)" id="flowchart-C-3" class="node default"><rect height="54" width="268.25" y="-27" x="-134.125" style="" class="basic label-container"></rect><g transform="translate(-104.125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="208.25"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLMEngine.from_engine_args</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 347)" id="flowchart-D-5" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>配置解析和验证</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 451)" id="flowchart-E-7" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模型加载阶段</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 555)" id="flowchart-F-9" class="node default"><rect height="54" width="220.28125" y="-27" x="-110.140625" style="" class="basic label-container"></rect><g transform="translate(-80.140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检测SmoothQuant量化</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 659)" id="flowchart-G-11" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>加载量化权重</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 763)" id="flowchart-H-13" class="node default"><rect height="54" width="179.859375" y="-27" x="-89.9296875" style="" class="basic label-container"></rect><g transform="translate(-59.9296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建W8A8量化层</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 867)" id="flowchart-I-15" class="node default"><rect height="54" width="158.609375" y="-27" x="-79.3046875" style="" class="basic label-container"></rect><g transform="translate(-49.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化KV缓存</p></span></div></foreignObject></g></g><g transform="translate(579.2265625, 1003)" id="flowchart-J-17" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内存检查</p></span></div></foreignObject></g></g><g transform="translate(845.453125, 1163)" id="flowchart-K-19" class="node default"><rect height="54" width="206.609375" y="-27" x="-103.3046875" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-73.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>KV缓存内存不足错误</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1163)" id="flowchart-L-21" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模型加载完成</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1267)" id="flowchart-M-23" class="node default"><rect height="54" width="187.921875" y="-27" x="-93.9609375" style="" class="basic label-container"></rect><g transform="translate(-63.9609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>generate函数调用</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1383)" id="flowchart-N-25" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>输入预处理</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1499)" id="flowchart-O-27" class="node default"><rect height="54" width="182.21875" y="-27" x="-91.109375" style="" class="basic label-container"></rect><g transform="translate(-61.109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Tokenization分词</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1603)" id="flowchart-P-29" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建请求对象</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1707)" id="flowchart-Q-31" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>推理主循环</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 1811)" id="flowchart-R-33" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调度器选择批次</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 1915)" id="flowchart-S-35" class="node default"><rect height="54" width="220.28125" y="-27" x="-110.140625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-80.140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SmoothQuant前向传播</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2019)" id="flowchart-T-37" class="node default"><rect height="54" width="211.265625" y="-27" x="-105.6328125" style="" class="basic label-container"></rect><g transform="translate(-75.6328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>激活量化 FP16→INT8</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2123)" id="flowchart-U-39" class="node default"><rect height="54" width="212" y="-27" x="-106" style="" class="basic label-container"></rect><g transform="translate(-76, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>量化GEMM INT8×INT8</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2227)" id="flowchart-V-41" class="node default"><rect height="54" width="235.65625" y="-27" x="-117.828125" style="" class="basic label-container"></rect><g transform="translate(-87.828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="175.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>反量化输出 INT32→FP16</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2331)" id="flowchart-W-43" class="node default"><rect height="54" width="159.828125" y="-27" x="-79.9140625" style="" class="basic label-container"></rect><g transform="translate(-49.9140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Attention计算</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2459)" id="flowchart-X-45" class="node default"><rect height="54" width="119.015625" y="-27" x="-59.5078125" style="" class="basic label-container"></rect><g transform="translate(-29.5078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="59.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FFN计算</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2563)" id="flowchart-Y-47" class="node default"><rect height="54" width="142.46875" y="-27" x="-71.234375" style="" class="basic label-container"></rect><g transform="translate(-41.234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="82.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Layer Norm</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2667)" id="flowchart-Z-49" class="node default"><rect height="54" width="134.109375" y="-27" x="-67.0546875" style="" class="basic label-container"></rect><g transform="translate(-37.0546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="74.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logits计算</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2771)" id="flowchart-AA-51" class="node default"><rect height="54" width="164.46875" y="-27" x="-82.234375" style="" class="basic label-container"></rect><g transform="translate(-52.234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>采样生成token</p></span></div></foreignObject></g></g><g transform="translate(125.828125, 2875)" id="flowchart-BB-53" class="node default"><rect height="54" width="142.609375" y="-27" x="-71.3046875" style="" class="basic label-container"></rect><g transform="translate(-41.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="82.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新KV缓存</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 3013.9375)" id="flowchart-CC-55" class="node default"><polygon transform="translate(-61.9375,61.9375)" class="label-container" points="61.9375,0 123.875,-61.9375 61.9375,-123.875 0,-61.9375"></polygon><g transform="translate(-34.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>序列完成?</p></span></div></foreignObject></g></g><g transform="translate(202.2421875, 3176.875)" id="flowchart-DD-59" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>输出结果</p></span></div></foreignObject></g></g><g transform="translate(845.453125, 1267)" id="flowchart-EE-61" class="node default"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解决方案</p></span></div></foreignObject></g></g><g transform="translate(452.2421875, 1383)" id="flowchart-FF-63" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>降低gpu_memory_utilization</p></span></div></foreignObject></g></g><g transform="translate(734.6953125, 1383)" id="flowchart-GG-65" class="node default"><rect height="54" width="204.90625" y="-27" x="-102.453125" style="" class="basic label-container"></rect><g transform="translate(-72.453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>减小max_model_len</p></span></div></foreignObject></g></g><g transform="translate(956.2109375, 1383)" id="flowchart-HH-67" class="node default"><rect height="54" width="138.125" y="-27" x="-69.0625" style="" class="basic label-container"></rect><g transform="translate(-39.0625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="78.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>使用多GPU</p></span></div></foreignObject></g></g><g transform="translate(1158.2578125, 1383)" id="flowchart-II-69" class="node default"><rect height="54" width="165.96875" y="-27" x="-82.984375" style="" class="basic label-container"></rect><g transform="translate(-52.984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="105.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调整block_size</p></span></div></foreignObject></g></g></g></g></g></svg>