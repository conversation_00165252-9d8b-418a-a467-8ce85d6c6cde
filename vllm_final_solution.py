#!/usr/bin/env python3
"""
vLLM CUDA Device-Side Assert 错误最终解决方案
通过强制使用V0引擎和优化内存配置来解决问题
"""

import os
import torch
import logging

# 设置CUDA调试环境变量
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA调用
os.environ['TORCH_USE_CUDA_DSA'] = '1'    # 启用设备端断言

# 强制使用V0引擎，避免V1引擎的内存问题
os.environ['VLLM_USE_V1'] = '0'           # 强制使用V0引擎
os.environ['VLLM_USE_TRITON_FLASH_ATTN'] = '0'  # 禁用Triton Flash Attention

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_cuda_environment():
    """检查CUDA环境"""
    print("=== CUDA环境检查 ===")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    print(f"✅ CUDA版本: {torch.version.cuda}")
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ 可用GPU数量: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        
        print(f"GPU {i}: {props.name}")
        print(f"  总内存: {total_memory:.2f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  计算能力: {props.major}.{props.minor}")
    
    return True

def test_vllm_v0_engine():
    """使用V0引擎测试vLLM"""
    print("\n=== vLLM V0引擎测试 ===")
    
    try:
        # 延迟导入vLLM，确保环境变量已设置
        from vllm import LLM, SamplingParams
        
        model_name = "/home/<USER>/single_llama"
        
        # 使用V0引擎的优化配置
        configs = [
            {
                "name": "V0配置1: 极简",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.8,  # 提高内存利用率
                "max_model_len": 64,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 16,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 1,
            },
            {
                "name": "V0配置2: 保守",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.9,  # 更高的内存利用率
                "max_model_len": 128,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 16,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 2,
            },
            {
                "name": "V0配置3: 标准",
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.95,  # 最高的内存利用率
                "max_model_len": 256,
                "enable_prefix_caching": False,
                "trust_remote_code": True,
                "block_size": 16,
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 4,
            }
        ]
        
        for config in configs:
            print(f"\n=== 尝试{config['name']} ===")
            
            try:
                torch.cuda.empty_cache()
                
                # 提取配置名称
                config_name = config.pop('name')
                
                print("配置参数:")
                for key, value in config.items():
                    print(f"  {key}: {value}")
                
                print("\n开始加载模型...")
                llm = LLM(model=model_name, **config)
                print(f"✅ {config_name}加载成功！")
                
                # 简单推理测试
                print("\n开始推理测试...")
                prompts = ["Hello"]
                sampling_params = SamplingParams(
                    temperature=0.0,
                    max_tokens=5,
                    use_beam_search=False
                )
                
                outputs = llm.generate(prompts, sampling_params)
                print(f"✅ {config_name}推理成功！")
                
                for output in outputs:
                    print(f"输入: {output.prompt}")
                    print(f"输出: {output.outputs[0].text}")
                
                return True, config_name
                
            except Exception as e:
                print(f"❌ {config_name}失败: {e}")
                torch.cuda.empty_cache()
                continue
        
        print("❌ 所有V0配置都失败了")
        return False, None
        
    except ImportError as e:
        print(f"❌ 无法导入vLLM: {e}")
        return False, None

def test_original_script():
    """测试原始的test2.py脚本是否现在能工作"""
    print("\n=== 测试原始脚本 ===")
    
    try:
        from vllm import LLM, SamplingParams
        
        model_name = "/home/<USER>/single_llama"
        
        # 使用原始脚本中的配置，但优化内存设置
        config = {
            "model": model_name,
            "tensor_parallel_size": 1,
            "max_model_len": 64,  # 减小序列长度
            "enable_prefix_caching": False,
            "trust_remote_code": True,
            "gpu_memory_utilization": 0.85,  # 提高内存利用率
            "block_size": 16,
            "enforce_eager": True,
            "disable_custom_all_reduce": True,
        }
        
        print("使用优化后的原始配置:")
        for key, value in config.items():
            if key != "model":
                print(f"  {key}: {value}")
        
        print("\n开始加载模型...")
        torch.cuda.empty_cache()
        
        llm = LLM(**config)
        print("✅ 原始脚本配置加载成功！")
        
        # 简单推理测试
        print("\n开始推理测试...")
        prompts = ["Hi"]
        sampling_params = SamplingParams(
            temperature=0.0,
            top_p=1.0,
            max_tokens=5
        )
        
        outputs = llm.generate(prompts, sampling_params)
        print("✅ 原始脚本推理成功！")
        
        for output in outputs:
            print(f"输入: {output.prompt}")
            print(f"输出: {output.outputs[0].text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 原始脚本测试失败: {e}")
        return False

def main():
    """主函数"""
    print("vLLM CUDA Device-Side Assert 错误最终解决方案")
    print("=" * 60)
    
    # 检查CUDA环境
    if not check_cuda_environment():
        print("❌ CUDA环境检查失败，无法继续")
        return False
    
    # 测试V0引擎配置
    success, config_name = test_vllm_v0_engine()
    if success:
        print(f"\n🎉 V0引擎测试成功！最佳配置: {config_name}")
        
        # 测试原始脚本是否现在能工作
        if test_original_script():
            print("\n🎉 原始脚本现在也能正常工作了！")
        
        print("\n✅ 解决方案总结:")
        print("1. 设置 VLLM_USE_V1=0 强制使用V0引擎")
        print("2. 设置 CUDA_LAUNCH_BLOCKING=1 启用同步CUDA调用")
        print("3. 设置 TORCH_USE_CUDA_DSA=1 启用设备端断言调试")
        print("4. 使用 enforce_eager=True 禁用CUDA图")
        print("5. 适当提高 gpu_memory_utilization 到 0.8-0.95")
        print("6. 使用较小的 max_model_len (64-256)")
        
        print("\n📝 在你的代码中应用这些设置:")
        print("```python")
        print("import os")
        print("os.environ['VLLM_USE_V1'] = '0'")
        print("os.environ['CUDA_LAUNCH_BLOCKING'] = '1'")
        print("os.environ['TORCH_USE_CUDA_DSA'] = '1'")
        print("")
        print("from vllm import LLM, SamplingParams")
        print("")
        print("llm = LLM(")
        print("    model='/home/<USER>/single_llama',")
        print("    tensor_parallel_size=1,")
        print("    gpu_memory_utilization=0.85,")
        print("    max_model_len=64,")
        print("    enforce_eager=True,")
        print("    trust_remote_code=True")
        print(")")
        print("```")
        
        return True
    
    print("\n❌ 所有配置都失败了")
    print("\n建议:")
    print("1. 检查模型文件路径是否正确")
    print("2. 尝试更小的模型进行测试")
    print("3. 检查GPU内存是否被其他进程占用")
    print("4. 考虑使用CPU模式进行测试")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
