# SmoothQuant新平台适配快速开始指南

## 1. 环境准备

### 1.1 依赖安装
```bash
# 基础依赖
sudo apt-get update
sudo apt-get install -y build-essential cmake git

# 数学库
sudo apt-get install -y libblas-dev liblapack-dev libopenblas-dev

# 并行计算
sudo apt-get install -y libomp-dev

# Python环境（用于验证）
pip install torch numpy scipy matplotlib
```

### 1.2 项目结构创建
```bash
mkdir smoothquant_adapter
cd smoothquant_adapter

# 创建目录结构
mkdir -p {src,include,tests,examples,docs,tools}
mkdir -p src/{core,operators,memory,utils}
mkdir -p include/{core,operators,memory,utils}
mkdir -p tests/{unit,integration,benchmark}

# 初始化CMake项目
cat > CMakeLists.txt << 'EOF'
cmake_minimum_required(VERSION 3.12)
project(SmoothQuantAdapter)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -march=native")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp")

# 查找依赖
find_package(OpenMP REQUIRED)
find_package(BLAS REQUIRED)

# 包含目录
include_directories(include)

# 添加子目录
add_subdirectory(src)
add_subdirectory(tests)
EOF
```

## 2. 核心组件实现

### 2.1 基础数据结构 (include/core/tensor.h)
```cpp
#pragma once
#include <vector>
#include <memory>
#include <initializer_list>

enum class DataType {
    FLOAT32, FLOAT16, INT8, INT32
};

class Tensor {
public:
    // 构造函数
    Tensor(const std::vector<int64_t>& shape, DataType dtype);
    Tensor(const std::vector<int64_t>& shape, DataType dtype, void* data);
    
    // 基础操作
    int64_t size(int dim) const { return shape_[dim]; }
    int64_t numel() const;
    DataType dtype() const { return dtype_; }
    void* data_ptr() const { return data_.get(); }
    
    // 形状操作
    Tensor view(const std::vector<int64_t>& new_shape) const;
    Tensor reshape(const std::vector<int64_t>& new_shape) const;
    
    // 数学操作
    Tensor operator*(const Tensor& other) const;
    Tensor operator+(const Tensor& other) const;
    Tensor matmul(const Tensor& other) const;
    
    // 量化相关
    std::pair<Tensor, Tensor> quantize_per_token() const;
    Tensor dequantize(const Tensor& scale) const;
    
private:
    std::vector<int64_t> shape_;
    DataType dtype_;
    std::shared_ptr<void> data_;
    size_t size_bytes_;
};
```

### 2.2 量化计算核心 (src/core/quantizer.cpp)
```cpp
#include "core/quantizer.h"
#include <algorithm>
#include <cmath>
#include <omp.h>

std::pair<Tensor, Tensor> SmoothQuantizer::quantize_activation(
    const Tensor& input, bool per_token) {
    
    if (per_token) {
        return quantize_per_token(input);
    } else {
        return quantize_per_tensor(input);
    }
}

std::pair<Tensor, Tensor> SmoothQuantizer::quantize_per_token(const Tensor& input) {
    // 输入形状: [batch*seq_len, hidden_dim]
    auto batch_seq = input.size(0);
    auto hidden_dim = input.size(1);
    
    // 创建输出张量
    auto quantized = Tensor({batch_seq, hidden_dim}, DataType::INT8);
    auto scales = Tensor({batch_seq, 1}, DataType::FLOAT32);
    
    auto input_ptr = static_cast<const float*>(input.data_ptr());
    auto quant_ptr = static_cast<int8_t*>(quantized.data_ptr());
    auto scale_ptr = static_cast<float*>(scales.data_ptr());
    
    #pragma omp parallel for
    for (int64_t i = 0; i < batch_seq; i++) {
        // 计算当前token的最大绝对值
        float max_val = 0.0f;
        for (int64_t j = 0; j < hidden_dim; j++) {
            max_val = std::max(max_val, std::abs(input_ptr[i * hidden_dim + j]));
        }
        
        // 计算缩放因子
        float scale = std::max(max_val / 127.0f, 1e-8f);
        scale_ptr[i] = scale;
        
        // 量化
        for (int64_t j = 0; j < hidden_dim; j++) {
            float val = input_ptr[i * hidden_dim + j] / scale;
            quant_ptr[i * hidden_dim + j] = static_cast<int8_t>(
                std::round(std::clamp(val, -128.0f, 127.0f)));
        }
    }
    
    return {quantized, scales};
}

Tensor SmoothQuantizer::quantized_linear(const Tensor& input, 
                                        const QuantizedLayer& layer) {
    // 1. 量化激活
    auto [qinput, input_scale] = quantize_activation(input, true);
    
    // 2. 执行量化GEMM
    auto result = quantized_gemm(qinput, layer.weight_int8, 
                                input_scale, layer.weight_scale);
    
    // 3. 添加偏置
    if (layer.bias.data_ptr() != nullptr) {
        result = result + layer.bias;
    }
    
    return result;
}
```

### 2.3 高性能GEMM实现 (src/operators/gemm.cpp)
```cpp
#include "operators/gemm.h"
#include <cblas.h>

Tensor OptimizedGEMM::quantized_gemm(const Tensor& qA, const Tensor& qB,
                                     const Tensor& scale_A, const Tensor& scale_B) {
    auto M = qA.size(0);
    auto K = qA.size(1);
    auto N = qB.size(1);
    
    // 创建输出张量
    auto output = Tensor({M, N}, DataType::FLOAT32);
    
    // 选择最优实现
    if (use_blas_ && M * N * K > blas_threshold_) {
        return gemm_with_blas(qA, qB, scale_A, scale_B);
    } else {
        return gemm_optimized_cpu(qA, qB, scale_A, scale_B);
    }
}

Tensor OptimizedGEMM::gemm_optimized_cpu(const Tensor& qA, const Tensor& qB,
                                        const Tensor& scale_A, const Tensor& scale_B) {
    auto M = qA.size(0);
    auto K = qA.size(1);
    auto N = qB.size(1);
    
    auto output = Tensor({M, N}, DataType::FLOAT32);
    
    auto qA_ptr = static_cast<const int8_t*>(qA.data_ptr());
    auto qB_ptr = static_cast<const int8_t*>(qB.data_ptr());
    auto scale_A_ptr = static_cast<const float*>(scale_A.data_ptr());
    auto scale_B_ptr = static_cast<const float*>(scale_B.data_ptr());
    auto output_ptr = static_cast<float*>(output.data_ptr());
    
    #pragma omp parallel for collapse(2)
    for (int64_t i = 0; i < M; i++) {
        for (int64_t j = 0; j < N; j++) {
            int32_t sum = 0;
            
            // 内积计算
            #pragma omp simd reduction(+:sum)
            for (int64_t k = 0; k < K; k++) {
                sum += static_cast<int32_t>(qA_ptr[i * K + k]) * 
                       static_cast<int32_t>(qB_ptr[k * N + j]);
            }
            
            // 应用缩放因子
            float scale = scale_A_ptr[i] * scale_B_ptr[j];
            output_ptr[i * N + j] = static_cast<float>(sum) * scale;
        }
    }
    
    return output;
}
```

## 3. 快速验证测试

### 3.1 单元测试 (tests/unit/test_quantizer.cpp)
```cpp
#include <gtest/gtest.h>
#include "core/quantizer.h"

class QuantizerTest : public ::testing::Test {
protected:
    void SetUp() override {
        quantizer_ = std::make_unique<SmoothQuantizer>();
    }
    
    std::unique_ptr<SmoothQuantizer> quantizer_;
};

TEST_F(QuantizerTest, PerTokenQuantization) {
    // 创建测试数据
    auto input = Tensor({2, 4}, DataType::FLOAT32);
    float* data = static_cast<float*>(input.data_ptr());
    
    // 填充测试数据
    std::vector<float> test_data = {
        1.0f, 2.0f, 3.0f, 4.0f,    // token 1
        -1.0f, -2.0f, -3.0f, -4.0f  // token 2
    };
    std::copy(test_data.begin(), test_data.end(), data);
    
    // 执行量化
    auto [quantized, scales] = quantizer_->quantize_activation(input, true);
    
    // 验证结果
    EXPECT_EQ(quantized.size(0), 2);
    EXPECT_EQ(quantized.size(1), 4);
    EXPECT_EQ(scales.size(0), 2);
    EXPECT_EQ(scales.size(1), 1);
    
    // 验证量化范围
    auto quant_ptr = static_cast<const int8_t*>(quantized.data_ptr());
    for (int i = 0; i < 8; i++) {
        EXPECT_GE(quant_ptr[i], -128);
        EXPECT_LE(quant_ptr[i], 127);
    }
}

TEST_F(QuantizerTest, QuantizedLinear) {
    // 创建测试层
    QuantizedLayer layer;
    layer.weight_int8 = Tensor({4, 3}, DataType::INT8);
    layer.weight_scale = Tensor({1, 3}, DataType::FLOAT32);
    
    // 创建输入
    auto input = Tensor({2, 4}, DataType::FLOAT32);
    
    // 执行量化线性层
    auto output = quantizer_->quantized_linear(input, layer);
    
    // 验证输出形状
    EXPECT_EQ(output.size(0), 2);
    EXPECT_EQ(output.size(1), 3);
}
```

### 3.2 性能基准测试 (tests/benchmark/bench_gemm.cpp)
```cpp
#include <benchmark/benchmark.h>
#include "operators/gemm.h"

static void BM_QuantizedGEMM(benchmark::State& state) {
    auto M = state.range(0);
    auto K = state.range(1);
    auto N = state.range(2);
    
    // 创建测试数据
    auto qA = Tensor({M, K}, DataType::INT8);
    auto qB = Tensor({K, N}, DataType::INT8);
    auto scale_A = Tensor({M, 1}, DataType::FLOAT32);
    auto scale_B = Tensor({1, N}, DataType::FLOAT32);
    
    OptimizedGEMM gemm_computer;
    
    for (auto _ : state) {
        auto result = gemm_computer.quantized_gemm(qA, qB, scale_A, scale_B);
        benchmark::DoNotOptimize(result);
    }
    
    // 计算性能指标
    auto ops = 2.0 * M * N * K;  // 乘加操作数
    state.counters["GOPS"] = benchmark::Counter(ops, benchmark::Counter::kIsRate);
}

// 注册不同规模的测试
BENCHMARK(BM_QuantizedGEMM)->Args({128, 512, 256});
BENCHMARK(BM_QuantizedGEMM)->Args({256, 1024, 512});
BENCHMARK(BM_QuantizedGEMM)->Args({512, 2048, 1024});

BENCHMARK_MAIN();
```

## 4. 编译和运行

### 4.1 编译项目
```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
make -j$(nproc)

# 运行测试
./tests/unit_tests
./tests/benchmark_tests
```

### 4.2 集成验证
```python
# tools/validate_accuracy.py
import torch
import numpy as np
import ctypes

# 加载编译的库
lib = ctypes.CDLL('./build/libsmoothquant_adapter.so')

def validate_with_pytorch():
    """与PyTorch实现对比验证精度"""
    # 创建测试数据
    input_data = torch.randn(32, 512, dtype=torch.float32)
    weight = torch.randn(512, 256, dtype=torch.float32)
    
    # PyTorch参考实现
    ref_output = torch.matmul(input_data, weight)
    
    # 我们的实现
    # ... 调用C++库进行计算 ...
    
    # 计算误差
    error = torch.abs(our_output - ref_output)
    max_error = torch.max(error).item()
    mean_error = torch.mean(error).item()
    
    print(f"Max error: {max_error:.6f}")
    print(f"Mean error: {mean_error:.6f}")
    
    return max_error < 0.01  # 1%误差阈值

if __name__ == "__main__":
    if validate_with_pytorch():
        print("✅ Accuracy validation passed!")
    else:
        print("❌ Accuracy validation failed!")
```

## 5. 下一步计划

### 5.1 立即行动项
1. **搭建基础框架** (1周)
   - 实现基础Tensor类
   - 完成量化核心算法
   - 编写单元测试

2. **性能优化** (2周)
   - SIMD指令优化
   - 多线程并行
   - 内存管理优化

3. **集成测试** (1周)
   - 端到端精度验证
   - 性能基准测试
   - 与vLLM对比

### 5.2 扩展功能
- GPU加速支持
- 动态批处理
- 模型格式转换工具
- 可视化调试工具

这个快速开始指南提供了实施SmoothQuant适配方案的具体步骤和代码示例，帮助你快速启动项目并验证核心功能。
