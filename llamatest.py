# from vllm import LLM,SamplingParams
 
# model_name="/home/<USER>/Meta-Llama-3.1-8B-Instruct"
 
# prompts = ["你好"]
# sampling_params=SamplingParams(temperature=0.0,top_p=1)
# llm=LLM(
# model=model_name,
# tensor_parallel_size=1,
# max_model_len=32768,
# enable_prefix_caching=True)
 
# outputs = llm.generate(prompts,sampling_params)
# for output in outputs:
#     prompt = output.prompt
#     generated_text = output.outputs[0].text
#     print(f"提示词:{prompt!r}，生成文本:{generated_text!r}")
from fastapi import FastAPI, Request
from pydantic import BaseModel
from vllm import LLM, SamplingParams

app = FastAPI()

# 初始化模型
model_name = "/home/<USER>/Meta-Llama-3.1-8B-Instruct"
llm = LLM(
    model=model_name,
    # quantization="awq",  # 指定使用 AWQ 量化
    tensor_parallel_size=1,
    max_model_len=32768,
    enable_prefix_caching=True
)

class PromptRequest(BaseModel):
    prompt: str

@app.post("/generate")
async def generate_text(request: PromptRequest):
    prompts = [request.prompt]
    sampling_params = SamplingParams(temperature=0.0, top_p=1)
    outputs = llm.generate(prompts, sampling_params)
    generated_text = outputs[0].outputs[0].text
    return {"prompt": request.prompt, "generated_text": generated_text}
#uvicorn llamatest:app --host 0.0.0.0 --port 8000 启动命令
#curl -X POST "http://localhost:8000/generate" -H "Content-Type: application/json" -d '{"prompt": "你好"}' 另一个终端测试